{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9JREVBX1BST0pFQ1QvZXhhbS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmVycm9yLmNhdXNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZG90LWFsbC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5zdGlja3kuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50ZXN0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuZW5kcy13aXRoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLm1hdGNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2gtYWxsLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnNwbGl0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3RhcnRzLXdpdGguanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwp2YXIgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9JREVBX1BST0pFQ1QvZXhhbS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyLmpzIikpOwp2YXIgX3JlZ2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovSURFQV9QUk9KRUNUL2V4YW0vcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3IuanMiKSk7CnZhciBfdG9Db25zdW1hYmxlQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9JREVBX1BST0pFQ1QvZXhhbS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90b0NvbnN1bWFibGVBcnJheS5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovSURFQV9QUk9KRUNUL2V4YW0vcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvci5qcyIpKTsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9JREVBX1BST0pFQ1QvZXhhbS9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX2RlZmluZVByb3BlcnR5MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovSURFQV9QUk9KRUNUL2V4YW0vcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZGVmaW5lUHJvcGVydHkuanMiKSk7CnZhciBfUXVlc3Rpb25DYXJkID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudHMvUXVlc3Rpb25DYXJkIikpOwp2YXIgX1F1ZXN0aW9uRm9ybSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnRzL1F1ZXN0aW9uRm9ybSIpKTsKdmFyIF9CYXRjaEltcG9ydCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnRzL0JhdGNoSW1wb3J0IikpOwp2YXIgX3F1ZXN0aW9uID0gcmVxdWlyZSgiQC9hcGkvYml6L3F1ZXN0aW9uIik7CnZhciBfcXVlc3Rpb25CYW5rID0gcmVxdWlyZSgiQC9hcGkvYml6L3F1ZXN0aW9uQmFuayIpOwp2YXIgX21ldGhvZHM7IC8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiUXVlc3Rpb25CYW5rRGV0YWlsIiwKICBjb21wb25lbnRzOiB7CiAgICBRdWVzdGlvbkNhcmQ6IF9RdWVzdGlvbkNhcmQuZGVmYXVsdCwKICAgIFF1ZXN0aW9uRm9ybTogX1F1ZXN0aW9uRm9ybS5kZWZhdWx0LAogICAgQmF0Y2hJbXBvcnQ6IF9CYXRjaEltcG9ydC5kZWZhdWx0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgdmFyIF9yZWY7CiAgICByZXR1cm4gX3JlZiA9IHsKICAgICAgLy8g6aKY5bqT5L+h5oGvCiAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgYmFua05hbWU6ICcnLAogICAgICAvLyDnu5/orqHmlbDmja4KICAgICAgc3RhdGlzdGljczogewogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHNpbmdsZUNob2ljZTogMCwKICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICBqdWRnbWVudDogMAogICAgICB9LAogICAgICAvLyDpopjnm67liJfooagKICAgICAgcXVlc3Rpb25MaXN0OiBbXSwKICAgICAgLy8g5YiG6aG15Y+C5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGJhbmtJZDogbnVsbCwKICAgICAgICBxdWVzdGlvblR5cGU6IG51bGwsCiAgICAgICAgZGlmZmljdWx0eTogbnVsbCwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IG51bGwKICAgICAgfSwKICAgICAgLy8g5bGV5byA54q25oCBCiAgICAgIGV4cGFuZEFsbDogZmFsc2UsCiAgICAgIC8vIOmAieaLqeeKtuaAgQogICAgICBzZWxlY3RlZFF1ZXN0aW9uczogW10KICAgIH0sICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9yZWYsICJzZWxlY3RlZFF1ZXN0aW9ucyIsIFtdKSwgImlzQWxsU2VsZWN0ZWQiLCBmYWxzZSksICJleHBhbmRlZFF1ZXN0aW9ucyIsIFtdKSwgInF1ZXN0aW9uRm9ybVZpc2libGUiLCBmYWxzZSksICJjdXJyZW50UXVlc3Rpb25UeXBlIiwgJ3NpbmdsZScpLCAiY3VycmVudFF1ZXN0aW9uRGF0YSIsIG51bGwpLCAiaW1wb3J0RHJhd2VyVmlzaWJsZSIsIGZhbHNlKSwgImJhdGNoSW1wb3J0VmlzaWJsZSIsIGZhbHNlKSwgImN1cnJlbnRJbXBvcnRNb2RlIiwgJ2V4Y2VsJyksICJkb2N1bWVudENvbnRlbnQiLCAnJyksICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9yZWYsICJkb2N1bWVudEh0bWxDb250ZW50IiwgJycpLCAicGFyc2VkUXVlc3Rpb25zIiwgW10pLCAicGFyc2VFcnJvcnMiLCBbXSksICJhbGxFeHBhbmRlZCIsIHRydWUpLCAiaXNTZXR0aW5nRnJvbUJhY2tlbmQiLCBmYWxzZSksICJkb2N1bWVudEltcG9ydERpYWxvZ1Zpc2libGUiLCBmYWxzZSksICJydWxlc0RpYWxvZ1Zpc2libGUiLCBmYWxzZSksICJhY3RpdmVSdWxlVGFiIiwgJ2V4YW1wbGVzJyksICJpc1VwbG9hZGluZyIsIGZhbHNlKSwgImlzUGFyc2luZyIsIGZhbHNlKSwgKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoX3JlZiwgImltcG9ydE9wdGlvbnMiLCB7CiAgICAgIHJldmVyc2U6IGZhbHNlLAogICAgICBhbGxvd0R1cGxpY2F0ZTogZmFsc2UKICAgIH0pLCAidXBsb2FkVXJsIiwgcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvYml6L3F1ZXN0aW9uQmFuay91cGxvYWREb2N1bWVudCcpLCAidXBsb2FkSGVhZGVycyIsIHsKICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgfSksICJ1cGxvYWREYXRhIiwge30pLCAicmljaEVkaXRvciIsIG51bGwpLCAiZWRpdG9ySW5pdGlhbGl6ZWQiLCBmYWxzZSk7CiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs5paH5qGj5YaF5a655Y+Y5YyW77yM6Ieq5Yqo6Kej5p6QCiAgICBkb2N1bWVudENvbnRlbnQ6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICAvLyDlpoLmnpzmmK/ku47lkI7nq6/orr7nva7lhoXlrrnvvIzkuI3op6blj5HliY3nq6/op6PmnpAKICAgICAgICBpZiAodGhpcy5pc1NldHRpbmdGcm9tQmFja2VuZCkgewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC50cmltKCkpIHsKICAgICAgICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50KCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW107CiAgICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW107CiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IGZhbHNlCiAgICB9LAogICAgLy8g55uR5ZCs5oq95bGJ5omT5byA54q25oCBCiAgICBpbXBvcnREcmF3ZXJWaXNpYmxlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgICBpZiAobmV3VmFsKSB7CiAgICAgICAgICAvLyDmir3lsYnmiZPlvIDml7bmuIXnqbrmiYDmnInlhoXlrrnlubbliJ3lp4vljJbnvJbovpHlmagKICAgICAgICAgIHRoaXMuY2xlYXJJbXBvcnRDb250ZW50KCk7CiAgICAgICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzLmluaXRSaWNoRWRpdG9yKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5oq95bGJ5YWz6Zet5pe26ZSA5q+B57yW6L6R5ZmoCiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCk7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGw7CiAgICAgICAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogZmFsc2UKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXRQYWdlKCk7CiAgICAvLyDliJvlu7rpmLLmipblh73mlbAKICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50ID0gdGhpcy5kZWJvdW5jZSh0aGlzLnBhcnNlRG9jdW1lbnQsIDEwMDApOwogICAgLy8g5Yid5aeL5YyW5LiK5Lyg5pWw5o2uCiAgICB0aGlzLnVwbG9hZERhdGEgPSB7CiAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQKICAgIH07CiAgICB0aGlzLnVwbG9hZEhlYWRlcnMgPSB7CiAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIHRoaXMuJHN0b3JlLmdldHRlcnMudG9rZW4KICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8g57yW6L6R5Zmo5bCG5Zyo5oq95bGJ5omT5byA5pe25Yid5aeL5YyWCiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g6ZSA5q+B5a+M5paH5pys57yW6L6R5ZmoCiAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCk7CiAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGw7CiAgICB9CiAgfSwKICBtZXRob2RzOiAoX21ldGhvZHMgPSB7CiAgICAvLyDliJ3lp4vljJbpobXpnaIKICAgIGluaXRQYWdlOiBmdW5jdGlvbiBpbml0UGFnZSgpIHsKICAgICAgdmFyIF90aGlzJCRyb3V0ZSRxdWVyeSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LAogICAgICAgIGJhbmtJZCA9IF90aGlzJCRyb3V0ZSRxdWVyeS5iYW5rSWQsCiAgICAgICAgYmFua05hbWUgPSBfdGhpcyQkcm91dGUkcXVlcnkuYmFua05hbWU7CiAgICAgIGlmICghYmFua0lkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57y65bCR6aKY5bqTSUTlj4LmlbAnKTsKICAgICAgICB0aGlzLmdvQmFjaygpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmJhbmtJZCA9IGJhbmtJZDsKICAgICAgdGhpcy5iYW5rTmFtZSA9IGJhbmtOYW1lIHx8ICfpopjlupPor6bmg4UnOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmJhbmtJZCA9IGJhbmtJZDsKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKTsKICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCk7CiAgICB9LAogICAgLy8g6L+U5Zue6aKY5bqT5YiX6KGoCiAgICBnb0JhY2s6IGZ1bmN0aW9uIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLmJhY2soKTsKICAgIH0sCiAgICAvLyDojrflj5bpopjnm67liJfooagKICAgIGdldFF1ZXN0aW9uTGlzdDogZnVuY3Rpb24gZ2V0UXVlc3Rpb25MaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgLy8g6L2s5o2i5p+l6K+i5Y+C5pWw5qC85byPCiAgICAgIHZhciBwYXJhbXMgPSB0aGlzLmNvbnZlcnRRdWVyeVBhcmFtcyh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgKDAsIF9xdWVzdGlvbi5saXN0UXVlc3Rpb24pKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIucXVlc3Rpb25MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKCfojrflj5bpopjnm67liJfooajlpLHotKUnKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6L2s5o2i5p+l6K+i5Y+C5pWw5qC85byPCiAgICBjb252ZXJ0UXVlcnlQYXJhbXM6IGZ1bmN0aW9uIGNvbnZlcnRRdWVyeVBhcmFtcyhwYXJhbXMpIHsKICAgICAgdmFyIGNvbnZlcnRlZFBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcGFyYW1zKTsKCiAgICAgIC8vIOi9rOaNoumimOWeiwogICAgICBpZiAoY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZSkgewogICAgICAgIHZhciB0eXBlTWFwID0gewogICAgICAgICAgJ3NpbmdsZSc6IDEsCiAgICAgICAgICAnbXVsdGlwbGUnOiAyLAogICAgICAgICAgJ2p1ZGdtZW50JzogMwogICAgICAgIH07CiAgICAgICAgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZSA9IHR5cGVNYXBbY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZV0gfHwgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZTsKICAgICAgfQoKICAgICAgLy8g6L2s5o2i6Zq+5bqmCiAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eSkgewogICAgICAgIHZhciBkaWZmaWN1bHR5TWFwID0gewogICAgICAgICAgJ+eugOWNlSc6IDEsCiAgICAgICAgICAn5Lit562JJzogMiwKICAgICAgICAgICflm7Dpmr4nOiAzCiAgICAgICAgfTsKICAgICAgICBjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXBbY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHldIHx8IGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5OwogICAgICB9CgogICAgICAvLyDmuIXnkIbnqbrlgLwKICAgICAgT2JqZWN0LmtleXMoY29udmVydGVkUGFyYW1zKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICBpZiAoY29udmVydGVkUGFyYW1zW2tleV0gPT09ICcnIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSBudWxsIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGRlbGV0ZSBjb252ZXJ0ZWRQYXJhbXNba2V5XTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gY29udmVydGVkUGFyYW1zOwogICAgfSwKICAgIC8vIOiOt+WPlue7n+iuoeaVsOaNrgogICAgZ2V0U3RhdGlzdGljczogZnVuY3Rpb24gZ2V0U3RhdGlzdGljcygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgICgwLCBfcXVlc3Rpb24uZ2V0UXVlc3Rpb25TdGF0aXN0aWNzKSh0aGlzLmJhbmtJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczMuc3RhdGlzdGljcyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIC8vIOS9v+eUqOaooeaLn+aVsOaNrgogICAgICAgIF90aGlzMy5zdGF0aXN0aWNzID0gewogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgICBtdWx0aXBsZUNob2ljZTogMCwKICAgICAgICAgIGp1ZGdtZW50OiAwCiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5om56YeP5a+85YWlCiAgICBoYW5kbGVCYXRjaEltcG9ydDogZnVuY3Rpb24gaGFuZGxlQmF0Y2hJbXBvcnQoKSB7CiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDlpITnkIbmibnph4/lr7zpopjmjInpkq7ngrnlh7sKICAgIGhhbmRsZUJhdGNoSW1wb3J0Q2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUJhdGNoSW1wb3J0Q2xpY2soKSB7CiAgICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy8g5re75Yqg6aKY55uuCiAgICBoYW5kbGVBZGRRdWVzdGlvbjogZnVuY3Rpb24gaGFuZGxlQWRkUXVlc3Rpb24odHlwZSkgewogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSB0eXBlOwogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbkRhdGEgPSBudWxsOwogICAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOWIh+aNouWxleW8gOeKtuaAgQogICAgdG9nZ2xlRXhwYW5kQWxsOiBmdW5jdGlvbiB0b2dnbGVFeHBhbmRBbGwoKSB7CiAgICAgIHRoaXMuZXhwYW5kQWxsID0gIXRoaXMuZXhwYW5kQWxsOwogICAgICBpZiAoIXRoaXMuZXhwYW5kQWxsKSB7CiAgICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucyA9IFtdOwogICAgICB9CiAgICB9LAogICAgLy8g5a+85Ye66aKY55uuCiAgICBoYW5kbGVFeHBvcnRRdWVzdGlvbnM6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydFF1ZXN0aW9ucygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHlr7zlh7rnmoTpopjnm64nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCJcdTZCNjNcdTU3MjhcdTVCRkNcdTUxRkEgIi5jb25jYXQodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFLi4uIikpOwogICAgICAvLyBUT0RPOiDlrp7njrDlr7zlh7rlip/og70KICAgIH0sCiAgICAvLyDliIfmjaLlhajpgIkv5YWo5LiN6YCJCiAgICBoYW5kbGVUb2dnbGVTZWxlY3RBbGw6IGZ1bmN0aW9uIGhhbmRsZVRvZ2dsZVNlbGVjdEFsbCgpIHsKICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gIXRoaXMuaXNBbGxTZWxlY3RlZDsKICAgICAgaWYgKHRoaXMuaXNBbGxTZWxlY3RlZCkgewogICAgICAgIC8vIOWFqOmAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSB0aGlzLnF1ZXN0aW9uTGlzdC5tYXAoZnVuY3Rpb24gKHEpIHsKICAgICAgICAgIHJldHVybiBxLnF1ZXN0aW9uSWQ7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCJcdTVERjJcdTkwMDlcdTYyRTkgIi5jb25jYXQodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFIikpOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWFqOS4jemAiQogICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSBbXTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWPlua2iOmAieaLqeaJgOaciemimOebricpOwogICAgICB9CiAgICB9LAogICAgLy8g5om56YeP5Yig6ZmkCiAgICBoYW5kbGVCYXRjaERlbGV0ZTogZnVuY3Rpb24gaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOmimOebricpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCJcdTc4NkVcdThCQTRcdTUyMjBcdTk2NjRcdTkwMDlcdTRFMkRcdTc2ODQgIi5jb25jYXQodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFXHU1NDE3XHVGRjFGIiksICfmibnph4/liKDpmaTnoa7orqQnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDov5nph4zlupTor6XosIPnlKjmibnph4/liKDpmaRBUEkKICAgICAgICAvLyDmmoLml7bkvb/nlKjljZXkuKrliKDpmaTnmoTmlrnlvI8KICAgICAgICB2YXIgZGVsZXRlUHJvbWlzZXMgPSBfdGhpczQuc2VsZWN0ZWRRdWVzdGlvbnMubWFwKGZ1bmN0aW9uIChxdWVzdGlvbklkKSB7CiAgICAgICAgICByZXR1cm4gKDAsIF9xdWVzdGlvbi5kZWxRdWVzdGlvbikocXVlc3Rpb25JZCk7CiAgICAgICAgfSk7CiAgICAgICAgUHJvbWlzZS5hbGwoZGVsZXRlUHJvbWlzZXMpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLnN1Y2Nlc3MoIlx1NjIxMFx1NTI5Rlx1NTIyMFx1OTY2NCAiLmNvbmNhdChfdGhpczQuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoLCAiIFx1OTA1M1x1OTg5OFx1NzZFRSIpKTsKICAgICAgICAgIF90aGlzNC5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdOwogICAgICAgICAgX3RoaXM0LmFsbFNlbGVjdGVkID0gZmFsc2U7CiAgICAgICAgICBfdGhpczQuZ2V0UXVlc3Rpb25MaXN0KCk7CiAgICAgICAgICBfdGhpczQuZ2V0U3RhdGlzdGljcygpOwogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLmVycm9yKCfmibnph4/liKDpmaTlpLHotKUnKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfSwgKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoX21ldGhvZHMsICJoYW5kbGVCYXRjaERlbGV0ZSIsIGZ1bmN0aW9uIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliKDpmaTnmoTpopjnm64nKTsKICAgICAgcmV0dXJuOwogICAgfQogICAgdGhpcy4kY29uZmlybSgiXHU3ODZFXHU4QkE0XHU1MjIwXHU5NjY0XHU5MDA5XHU0RTJEXHU3Njg0ICIuY29uY2F0KHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoLCAiIFx1OTA1M1x1OTg5OFx1NzZFRVx1NTQxN1x1RkYxRiIpLCAn5om56YeP5Yig6ZmkJywgewogICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICB0eXBlOiAnd2FybmluZycKICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAvLyDmibnph4/liKDpmaRBUEnosIPnlKgKICAgICAgdmFyIGRlbGV0ZVByb21pc2VzID0gX3RoaXM1LnNlbGVjdGVkUXVlc3Rpb25zLm1hcChmdW5jdGlvbiAocXVlc3Rpb25JZCkgewogICAgICAgIHJldHVybiAoMCwgX3F1ZXN0aW9uLmRlbFF1ZXN0aW9uKShxdWVzdGlvbklkKTsKICAgICAgfSk7CiAgICAgIFByb21pc2UuYWxsKGRlbGV0ZVByb21pc2VzKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczUuJG1lc3NhZ2Uuc3VjY2VzcygiXHU2MjEwXHU1MjlGXHU1MjIwXHU5NjY0ICIuY29uY2F0KF90aGlzNS5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFIikpOwogICAgICAgIF90aGlzNS5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdOwogICAgICAgIF90aGlzNS5pc0FsbFNlbGVjdGVkID0gZmFsc2U7CiAgICAgICAgX3RoaXM1LmdldFF1ZXN0aW9uTGlzdCgpOwogICAgICAgIF90aGlzNS5nZXRTdGF0aXN0aWNzKCk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzNS4kbWVzc2FnZS5lcnJvcign5om56YeP5Yig6Zmk5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICBfdGhpczUuJG1lc3NhZ2UuaW5mbygn5bey5Y+W5raI5Yig6ZmkJyk7CiAgICB9KTsKICB9KSwgImhhbmRsZVF1ZXN0aW9uU2VsZWN0IiwgZnVuY3Rpb24gaGFuZGxlUXVlc3Rpb25TZWxlY3QocXVlc3Rpb25JZCwgc2VsZWN0ZWQpIHsKICAgIGlmIChzZWxlY3RlZCkgewogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuaW5jbHVkZXMocXVlc3Rpb25JZCkpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLnB1c2gocXVlc3Rpb25JZCk7CiAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgIHZhciBpbmRleCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuaW5kZXhPZihxdWVzdGlvbklkKTsKICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0KCiAgICAvLyDmm7TmlrDlhajpgInnirbmgIEKICAgIHRoaXMuaXNBbGxTZWxlY3RlZCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSB0aGlzLnF1ZXN0aW9uTGlzdC5sZW5ndGg7CiAgfSksICJoYW5kbGVUb2dnbGVFeHBhbmQiLCBmdW5jdGlvbiBoYW5kbGVUb2dnbGVFeHBhbmQocXVlc3Rpb25JZCkgewogICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICB2YXIgaW5kZXggPSB0aGlzLmV4cGFuZGVkUXVlc3Rpb25zLmluZGV4T2YocXVlc3Rpb25JZCk7CiAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAvLyDmlLbotbfpopjnm64KICAgICAgdGhpcy5leHBhbmRlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpOwogICAgICAvLyDlpoLmnpzlvZPliY3mmK8i5bGV5byA5omA5pyJIueKtuaAge+8jOWImeWPlua2iCLlsZXlvIDmiYDmnIki54q25oCBCiAgICAgIGlmICh0aGlzLmV4cGFuZEFsbCkgewogICAgICAgIHRoaXMuZXhwYW5kQWxsID0gZmFsc2U7CiAgICAgICAgLy8g5bCG5YW25LuW6aKY55uu5re75Yqg5YiwZXhwYW5kZWRRdWVzdGlvbnPmlbDnu4TkuK3vvIzpmaTkuoblvZPliY3opoHmlLbotbfnmoTpopjnm64KICAgICAgICB0aGlzLnF1ZXN0aW9uTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChxdWVzdGlvbikgewogICAgICAgICAgaWYgKHF1ZXN0aW9uLnF1ZXN0aW9uSWQgIT09IHF1ZXN0aW9uSWQgJiYgIV90aGlzNi5leHBhbmRlZFF1ZXN0aW9ucy5pbmNsdWRlcyhxdWVzdGlvbi5xdWVzdGlvbklkKSkgewogICAgICAgICAgICBfdGhpczYuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbi5xdWVzdGlvbklkKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSBlbHNlIHsKICAgICAgLy8g5bGV5byA6aKY55uuCiAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbklkKTsKICAgIH0KICB9KSwgImhhbmRsZUVkaXRRdWVzdGlvbiIsIGZ1bmN0aW9uIGhhbmRsZUVkaXRRdWVzdGlvbihxdWVzdGlvbikgewogICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gcXVlc3Rpb247CiAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSBxdWVzdGlvbi5xdWVzdGlvblR5cGU7CiAgICB0aGlzLnF1ZXN0aW9uRm9ybVZpc2libGUgPSB0cnVlOwogIH0pLCAiaGFuZGxlQ29weVF1ZXN0aW9uIiwgZnVuY3Rpb24gaGFuZGxlQ29weVF1ZXN0aW9uKHF1ZXN0aW9uKSB7CiAgICAvLyDliJvlu7rlpI3liLbnmoTpopjnm67mlbDmja7vvIjnp7vpmaRJROebuOWFs+Wtl+aute+8iQogICAgdmFyIGNvcGllZFF1ZXN0aW9uID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcXVlc3Rpb24pLCB7fSwgewogICAgICBxdWVzdGlvbklkOiBudWxsLAogICAgICAvLyDmuIXpmaRJRO+8jOihqOekuuaWsOWingogICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICBjcmVhdGVCeTogbnVsbCwKICAgICAgdXBkYXRlQnk6IG51bGwKICAgIH0pOwoKICAgIC8vIOiuvue9ruS4uue8lui+keaooeW8j+W5tuaJk+W8gOihqOWNlQogICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gY29waWVkUXVlc3Rpb247CiAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGUgPSB0aGlzLmNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyhxdWVzdGlvbi5xdWVzdGlvblR5cGUpOwogICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gdHJ1ZTsKICB9KSwgImNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyIsIGZ1bmN0aW9uIGNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyh0eXBlKSB7CiAgICB2YXIgdHlwZU1hcCA9IHsKICAgICAgMTogJ3NpbmdsZScsCiAgICAgIDI6ICdtdWx0aXBsZScsCiAgICAgIDM6ICdqdWRnbWVudCcKICAgIH07CiAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCB0eXBlOwogIH0pLCAiaGFuZGxlRGVsZXRlUXVlc3Rpb24iLCBmdW5jdGlvbiBoYW5kbGVEZWxldGVRdWVzdGlvbihxdWVzdGlvbikgewogICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICB2YXIgcXVlc3Rpb25Db250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpOwogICAgdmFyIGRpc3BsYXlDb250ZW50ID0gcXVlc3Rpb25Db250ZW50Lmxlbmd0aCA+IDUwID8gcXVlc3Rpb25Db250ZW50LnN1YnN0cmluZygwLCA1MCkgKyAnLi4uJyA6IHF1ZXN0aW9uQ29udGVudDsKICAgIHRoaXMuJGNvbmZpcm0oIlx1Nzg2RVx1OEJBNFx1NTIyMFx1OTY2NFx1OTg5OFx1NzZFRVwiIi5jb25jYXQoZGlzcGxheUNvbnRlbnQsICJcIlx1NTQxN1x1RkYxRiIpLCAn5o+Q56S6JywgewogICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICB0eXBlOiAnd2FybmluZycKICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAoMCwgX3F1ZXN0aW9uLmRlbFF1ZXN0aW9uKShxdWVzdGlvbi5xdWVzdGlvbklkKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczcuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJyk7CiAgICAgICAgX3RoaXM3LmdldFF1ZXN0aW9uTGlzdCgpOwogICAgICAgIF90aGlzNy5nZXRTdGF0aXN0aWNzKCk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk6aKY55uu5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSk7CiAgfSksICJoYW5kbGVRdWVzdGlvbkZvcm1TdWNjZXNzIiwgZnVuY3Rpb24gaGFuZGxlUXVlc3Rpb25Gb3JtU3VjY2VzcygpIHsKICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKTsKICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogIH0pLCAiaGFuZGxlQmF0Y2hJbXBvcnRTdWNjZXNzIiwgZnVuY3Rpb24gaGFuZGxlQmF0Y2hJbXBvcnRTdWNjZXNzKCkgewogICAgdGhpcy5iYXRjaEltcG9ydFZpc2libGUgPSBmYWxzZTsKICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IGZhbHNlOwogICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKTsKICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpOwogIH0pLCAiaGFuZGxlRHJhd2VyQ2xvc2UiLCBmdW5jdGlvbiBoYW5kbGVEcmF3ZXJDbG9zZShkb25lKSB7CiAgICBkb25lKCk7CiAgfSksICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9tZXRob2RzLCAiY2xlYXJJbXBvcnRDb250ZW50IiwgZnVuY3Rpb24gY2xlYXJJbXBvcnRDb250ZW50KCkgewogICAgLy8g5riF56m65paH5qGj5YaF5a65CiAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9ICcnOwogICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gJyc7CgogICAgLy8g5riF56m66Kej5p6Q57uT5p6cCiAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdOwogICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdOwoKICAgIC8vIOmHjee9ruino+aekOeKtuaAgQogICAgdGhpcy5hbGxFeHBhbmRlZCA9IHRydWU7CiAgICB0aGlzLmlzU2V0dGluZ0Zyb21CYWNrZW5kID0gZmFsc2U7CgogICAgLy8g6YeN572u5LiK5Lyg54q25oCBCiAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2U7CiAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlOwoKICAgIC8vIOmHjee9ruWvvOWFpemAiemhuQogICAgdGhpcy5pbXBvcnRPcHRpb25zID0gewogICAgICByZXZlcnNlOiBmYWxzZSwKICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICB9OwogIH0pLCAic2hvd0RvY3VtZW50SW1wb3J0RGlhbG9nIiwgZnVuY3Rpb24gc2hvd0RvY3VtZW50SW1wb3J0RGlhbG9nKCkgewogICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAvLyDmuIXpmaTkuIrkuIDmrKHnmoTkuIrkvKDnirbmgIHlkozlhoXlrrkKICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2U7CgogICAgLy8g5riF6Zmk5LiK5Lyg57uE5Lu255qE5paH5Lu25YiX6KGoCiAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIHZhciB1cGxvYWRDb21wb25lbnQgPSBfdGhpczguJHJlZnMuZG9jdW1lbnRVcGxvYWQ7CiAgICAgIGlmICh1cGxvYWRDb21wb25lbnQpIHsKICAgICAgICB1cGxvYWRDb21wb25lbnQuY2xlYXJGaWxlcygpOwogICAgICB9CiAgICB9KTsKICAgIHRoaXMuZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICB9KSwgInNob3dSdWxlc0RpYWxvZyIsIGZ1bmN0aW9uIHNob3dSdWxlc0RpYWxvZygpIHsKICAgIHRoaXMuYWN0aXZlUnVsZVRhYiA9ICdleGFtcGxlcyc7IC8vIOm7mOiupOaYvuekuuiMg+S+i+agh+etvumhtQogICAgdGhpcy5ydWxlc0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogIH0pLCAiY29weUV4YW1wbGVUb0VkaXRvciIsIGZ1bmN0aW9uIGNvcHlFeGFtcGxlVG9FZGl0b3IoKSB7CiAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgIC8vIOS9v+eUqOi+k+WFpeiMg+S+i+agh+etvumhtemHjOeahOWJjTPpopjlhoXlrrnvvIzovazmjaLkuLpIVE1M5qC85byPCiAgICB2YXIgaHRtbFRlbXBsYXRlID0gIlxuPHA+MS5cdUZGMDggIFx1RkYwOVx1NjYyRlx1NjIxMVx1NTZGRFx1NjcwMFx1NjVFOVx1NzY4NFx1OEJEN1x1NkI0Q1x1NjAzQlx1OTZDNlx1RkYwQ1x1NTNDOFx1NzlGMFx1NEY1Q1wiXHU4QkQ3XHU0RTA5XHU3NjdFXCJcdTMwMDI8L3A+XG48cD5BLlx1MzAwQVx1NURFNlx1NEYyMFx1MzAwQjwvcD5cbjxwPkIuXHUzMDBBXHU3OUJCXHU5QTlBXHUzMDBCPC9wPlxuPHA+Qy5cdTMwMEFcdTU3NUJcdTdFQ0ZcdTMwMEI8L3A+XG48cD5ELlx1MzAwQVx1OEJEN1x1N0VDRlx1MzAwQjwvcD5cbjxwPlx1N0I1NFx1Njg0OFx1RkYxQUQ8L3A+XG48cD5cdTg5RTNcdTY3OTBcdUZGMUFcdThCRDdcdTdFQ0ZcdTY2MkZcdTYyMTFcdTU2RkRcdTY3MDBcdTY1RTlcdTc2ODRcdThCRDdcdTZCNENcdTYwM0JcdTk2QzZcdTMwMDI8L3A+XG48cD5cdTk2QkVcdTVFQTZcdUZGMUFcdTRFMkRcdTdCNDk8L3A+XG48cD48YnI+PC9wPlxuXG48cD4yLlx1NEUyRFx1NTM0RVx1NEVCQVx1NkMxMVx1NTE3MVx1NTQ4Q1x1NTZGRFx1NzY4NFx1NjIxMFx1N0FDQlx1RkYwQ1x1NjgwN1x1NUZEN1x1Nzc0MFx1RkYwOCBcdUZGMDlcdTMwMDI8L3A+XG48cD5BLlx1NEUyRFx1NTZGRFx1NjVCMFx1NkMxMVx1NEUzQlx1NEUzQlx1NEU0OVx1OTc2OVx1NTQ3RFx1NTNENlx1NUY5N1x1NEU4Nlx1NTdGQVx1NjcyQ1x1ODBEQ1x1NTIyOTwvcD5cbjxwPkIuXHU0RTJEXHU1NkZEXHU3M0IwXHU0RUUzXHU1M0YyXHU3Njg0XHU1RjAwXHU1OUNCPC9wPlxuPHA+Qy5cdTUzNEFcdTZCOTZcdTZDMTFcdTU3MzBcdTUzNEFcdTVDMDFcdTVFRkFcdTc5M0VcdTRGMUFcdTc2ODRcdTdFRDNcdTY3NUY8L3A+XG48cD5ELlx1NEUyRFx1NTZGRFx1OEZEQlx1NTE2NVx1NzkzRVx1NEYxQVx1NEUzQlx1NEU0OVx1NzkzRVx1NEYxQTwvcD5cbjxwPlx1N0I1NFx1Njg0OFx1RkYxQUFCQzwvcD5cbjxwPlx1ODlFM1x1Njc5MFx1RkYxQVx1NjVCMFx1NEUyRFx1NTZGRFx1NzY4NFx1NjIxMFx1N0FDQlx1RkYwQ1x1NjgwN1x1NUZEN1x1Nzc0MFx1NjIxMVx1NTZGRFx1NjVCMFx1NkMxMVx1NEUzQlx1NEUzQlx1NEU0OVx1OTc2OVx1NTQ3RFx1OTYzNlx1NkJCNVx1NzY4NFx1NTdGQVx1NjcyQ1x1N0VEM1x1Njc1Rlx1NTQ4Q1x1NzkzRVx1NEYxQVx1NEUzQlx1NEU0OVx1OTc2OVx1NTQ3RFx1OTYzNlx1NkJCNVx1NzY4NFx1NUYwMFx1NTlDQlx1MzAwMjwvcD5cbjxwPjxicj48L3A+XG5cbjxwPjMuXHU1MTQzXHU2NzQyXHU1MjY3XHU3Njg0XHU1NkRCXHU1OTI3XHU2MEIyXHU1MjY3XHU2NjJGXHVGRjFBXHU1MTczXHU2QzQ5XHU1MzdGXHU3Njg0XHUzMDBBXHU3QUE2XHU1QTI1XHU1MUE0XHUzMDBCXHVGRjBDXHU5QTZDXHU4MUY0XHU4RkRDXHU3Njg0XHUzMDBBXHU2QzQ5XHU1QkFCXHU3OUNCXHUzMDBCXHVGRjBDXHU3NjdEXHU2NzM0XHU3Njg0XHUzMDBBXHU2OEE3XHU2ODUwXHU5NkU4XHUzMDBCXHU1NDhDXHU5MEQxXHU1MTQ5XHU3OTU2XHU3Njg0XHUzMDBBXHU4RDc1XHU2QzBGXHU1QjY0XHU1MTNGXHUzMDBCXHUzMDAyPC9wPlxuPHA+XHU3QjU0XHU2ODQ4XHVGRjFBXHU5NTE5XHU4QkVGPC9wPlxuPHA+XHU4OUUzXHU2NzkwXHVGRjFBXHU1MTQzXHU2NzQyXHU1MjY3XHUzMDBBXHU4RDc1XHU2QzBGXHU1QjY0XHU1MTNGXHUzMDBCXHU1MTY4XHU1NDBEXHUzMDBBXHU1MUE0XHU2MkE1XHU1MUE0XHU4RDc1XHU2QzBGXHU1QjY0XHU1MTNGXHUzMDBCXHVGRjBDXHU0RTNBXHU3RUFBXHU1NDFCXHU3OTY1XHU2MjQwXHU0RjVDXHUzMDAyPC9wPlxuICAgICAgIi50cmltKCk7CgogICAgLy8g55u05o6l6K6+572u5Yiw5a+M5paH5pys57yW6L6R5ZmoCiAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoaHRtbFRlbXBsYXRlKTsKICAgIH0gZWxzZSB7CiAgICAgIC8vIOWmguaenOe8lui+keWZqOacquWIneWni+WMlu+8jOetieW+heWIneWni+WMluWQjuWGjeiuvue9rgogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgaWYgKF90aGlzOS5yaWNoRWRpdG9yICYmIF90aGlzOS5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgICAgX3RoaXM5LnJpY2hFZGl0b3Iuc2V0RGF0YShodG1sVGVtcGxhdGUpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CgogICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICB0aGlzLnJ1bGVzRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwoKICAgIC8vIOaPkOekuueUqOaItwogICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfovpPlhaXojIPkvovlt7LloavlhYXliLDnvJbovpHljLrvvIzlj7PkvqflsIboh6rliqjop6PmnpAnKTsKICB9KSwgImRvd25sb2FkRXhjZWxUZW1wbGF0ZSIsIGZ1bmN0aW9uIGRvd25sb2FkRXhjZWxUZW1wbGF0ZSgpIHsKICAgIHRoaXMuZG93bmxvYWQoJ2Jpei9xdWVzdGlvbkJhbmsvZG93bmxvYWRFeGNlbFRlbXBsYXRlJywge30sICJcdTk4OThcdTc2RUVcdTVCRkNcdTUxNjVFeGNlbFx1NkEyMVx1Njc3Ri54bHN4Iik7CiAgfSksICJkb3dubG9hZFdvcmRUZW1wbGF0ZSIsIGZ1bmN0aW9uIGRvd25sb2FkV29yZFRlbXBsYXRlKCkgewogICAgdGhpcy5kb3dubG9hZCgnYml6L3F1ZXN0aW9uQmFuay9kb3dubG9hZFdvcmRUZW1wbGF0ZScsIHt9LCAiXHU5ODk4XHU3NkVFXHU1QkZDXHU1MTY1V29yZFx1NkEyMVx1Njc3Ri5kb2N4Iik7CiAgfSksICJiZWZvcmVVcGxvYWQiLCBmdW5jdGlvbiBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgdmFyIGlzVmFsaWRUeXBlID0gZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIHx8IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyB8fCBmaWxlLm5hbWUuZW5kc1dpdGgoJy5kb2N4JykgfHwgZmlsZS5uYW1lLmVuZHNXaXRoKCcueGxzeCcpOwogICAgdmFyIGlzTHQxME0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOwogICAgaWYgKCFpc1ZhbGlkVHlwZSkgewogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKAgLmRvY3gg5oiWIC54bHN4IOagvOW8j+eahOaWh+S7tiEnKTsKICAgICAgcmV0dXJuIGZhbHNlOwogICAgfQogICAgaWYgKCFpc0x0MTBNKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpOwogICAgICByZXR1cm4gZmFsc2U7CiAgICB9CgogICAgLy8g5pu05paw5LiK5Lyg5pWw5o2uCiAgICB0aGlzLnVwbG9hZERhdGEuYmFua0lkID0gdGhpcy5iYW5rSWQ7CgogICAgLy8g6K6+572u5LiK5Lyg54q25oCBCiAgICB0aGlzLmlzVXBsb2FkaW5nID0gdHJ1ZTsKICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2U7CiAgICByZXR1cm4gdHJ1ZTsKICB9KSwgImhhbmRsZVVwbG9hZFN1Y2Nlc3MiLCBmdW5jdGlvbiBoYW5kbGVVcGxvYWRTdWNjZXNzKHJlc3BvbnNlLCBmaWxlKSB7CiAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgLy8g5LiK5Lyg5a6M5oiQ77yM5byA5aeL6Kej5p6QCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy5pc1BhcnNpbmcgPSB0cnVlOwoKICAgICAgLy8g5riF6Zmk5LmL5YmN55qE6Kej5p6Q57uT5p6c77yM56Gu5L+d5bmy5YeA55qE5byA5aeLCiAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW107CiAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBbXTsKCiAgICAgIC8vIOW7tui/n+WFs+mXreWvueivneahhu+8jOiuqeeUqOaIt+eci+WIsOino+aekOWKqOeUuwogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczAuZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgX3RoaXMwLmlzUGFyc2luZyA9IGZhbHNlOwogICAgICB9LCAxNTAwKTsKCiAgICAgIC8vIOiuvue9ruagh+W/l+S9je+8jOmBv+WFjeinpuWPkeWJjeerr+mHjeaWsOino+aekAogICAgICB0aGlzLmlzU2V0dGluZ0Zyb21CYWNrZW5kID0gdHJ1ZTsKCiAgICAgIC8vIOWwhuino+aekOe7k+aenOaYvuekuuWcqOWPs+S+pwogICAgICBpZiAocmVzcG9uc2UucXVlc3Rpb25zICYmIHJlc3BvbnNlLnF1ZXN0aW9ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSByZXNwb25zZS5xdWVzdGlvbnMubWFwKGZ1bmN0aW9uIChxdWVzdGlvbikgewogICAgICAgICAgcmV0dXJuICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHF1ZXN0aW9uKSwge30sIHsKICAgICAgICAgICAgY29sbGFwc2VkOiBmYWxzZSAvLyDpu5jorqTlsZXlvIAKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICAgIC8vIOmHjee9ruWFqOmDqOWxleW8gOeKtuaAgQogICAgICAgIHRoaXMuYWxsRXhwYW5kZWQgPSB0cnVlOwogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSByZXNwb25zZS5lcnJvcnMgfHwgW107CgogICAgICAgIC8vIOaYvuekuuivpue7hueahOino+aekOe7k+aenAogICAgICAgIHZhciBlcnJvckNvdW50ID0gcmVzcG9uc2UuZXJyb3JzID8gcmVzcG9uc2UuZXJyb3JzLmxlbmd0aCA6IDA7CiAgICAgICAgaWYgKGVycm9yQ291bnQgPiAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIlx1NjIxMFx1NTI5Rlx1ODlFM1x1Njc5MFx1NTFGQSAiLmNvbmNhdChyZXNwb25zZS5xdWVzdGlvbnMubGVuZ3RoLCAiIFx1OTA1M1x1OTg5OFx1NzZFRVx1RkYwQ1x1NjcwOSAiKS5jb25jYXQoZXJyb3JDb3VudCwgIiBcdTRFMkFcdTk1MTlcdThCRUZcdTYyMTZcdThCNjZcdTU0NEEiKSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcygiXHU2MjEwXHU1MjlGXHU4OUUzXHU2NzkwXHU1MUZBICIuY29uY2F0KHJlc3BvbnNlLnF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFIikpOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrop6PmnpDlh7rku7vkvZXpopjnm67vvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8nKTsKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdOwogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSByZXNwb25zZS5lcnJvcnMgfHwgWyfmnKrog73op6PmnpDlh7rpopjnm67lhoXlrrknXTsKICAgICAgfQoKICAgICAgLy8g5bCG5Y6f5aeL5YaF5a655aGr5YWF5Yiw5a+M5paH5pys57yW6L6R5Zmo5LitCiAgICAgIGlmIChyZXNwb25zZS5vcmlnaW5hbENvbnRlbnQpIHsKICAgICAgICB0aGlzLnNldEVkaXRvckNvbnRlbnQocmVzcG9uc2Uub3JpZ2luYWxDb250ZW50KTsKICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudDsKICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSByZXNwb25zZS5vcmlnaW5hbENvbnRlbnQ7IC8vIOWIneWni+WMlkhUTUzlhoXlrrkKICAgICAgfQoKICAgICAgLy8g5bu26L+f6YeN572u5qCH5b+X5L2N77yM56Gu5L+d5omA5pyJ5byC5q2l5pON5L2c5a6M5oiQCiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMC5pc1NldHRpbmdGcm9tQmFja2VuZCA9IGZhbHNlOwogICAgICB9LCAyMDAwKTsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofku7bkuIrkvKDlpLHotKUnKTsKICAgICAgLy8g6YeN572u54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZTsKICAgIH0KICB9KSwgImhhbmRsZVVwbG9hZEVycm9yIiwgZnVuY3Rpb24gaGFuZGxlVXBsb2FkRXJyb3IoZXJyb3IsIGZpbGUpIHsKICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pe+8jOivt+ajgOafpee9kee7nOi/nuaOpeaIluiBlOezu+euoeeQhuWRmCcpOwoKICAgIC8vIOmHjee9rueKtuaAgQogICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlOwogICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZTsKICB9KSwgImNvbGxhcHNlQWxsIiwgZnVuY3Rpb24gY29sbGFwc2VBbGwoKSB7CiAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zLmZvckVhY2goZnVuY3Rpb24gKHF1ZXN0aW9uKSB7CiAgICAgIF90aGlzMS4kc2V0KHF1ZXN0aW9uLCAnY29sbGFwc2VkJywgdHJ1ZSk7CiAgICB9KTsKICB9KSwgKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoX21ldGhvZHMsICJ0b2dnbGVRdWVzdGlvbiIsIGZ1bmN0aW9uIHRvZ2dsZVF1ZXN0aW9uKGluZGV4KSB7CiAgICB2YXIgcXVlc3Rpb24gPSB0aGlzLnBhcnNlZFF1ZXN0aW9uc1tpbmRleF07CiAgICB0aGlzLiRzZXQocXVlc3Rpb24sICdjb2xsYXBzZWQnLCAhcXVlc3Rpb24uY29sbGFwc2VkKTsKICB9KSwgInRvZ2dsZUFsbFF1ZXN0aW9ucyIsIGZ1bmN0aW9uIHRvZ2dsZUFsbFF1ZXN0aW9ucygpIHsKICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgIHRoaXMuYWxsRXhwYW5kZWQgPSAhdGhpcy5hbGxFeHBhbmRlZDsKICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zLmZvckVhY2goZnVuY3Rpb24gKHF1ZXN0aW9uKSB7CiAgICAgIF90aGlzMTAuJHNldChxdWVzdGlvbiwgJ2NvbGxhcHNlZCcsICFfdGhpczEwLmFsbEV4cGFuZGVkKTsKICAgIH0pOwogIH0pLCAiY29uZmlybUltcG9ydCIsIGZ1bmN0aW9uIGNvbmZpcm1JbXBvcnQoKSB7CiAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CiAgICBpZiAodGhpcy5wYXJzZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5rKh5pyJ5Y+v5a+85YWl55qE6aKY55uuJyk7CiAgICAgIHJldHVybjsKICAgIH0KICAgIHRoaXMuJGNvbmZpcm0oIlx1Nzg2RVx1OEJBNFx1NUJGQ1x1NTE2NSAiLmNvbmNhdCh0aGlzLnBhcnNlZFF1ZXN0aW9ucy5sZW5ndGgsICIgXHU5MDUzXHU5ODk4XHU3NkVFXHU1NDE3XHVGRjFGIiksICfnoa7orqTlr7zlhaUnLCB7CiAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgIHR5cGU6ICdpbmZvJwogICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzMTEuaW1wb3J0UXVlc3Rpb25zKCk7CiAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgfSksICJpbXBvcnRRdWVzdGlvbnMiLCBmdW5jdGlvbiBpbXBvcnRRdWVzdGlvbnMoKSB7CiAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHZhciBxdWVzdGlvbnNUb0ltcG9ydCwgaW1wb3J0RGF0YSwgcmVzcG9uc2UsIF90OwogICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dCkgewogICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Lm4pIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgX2NvbnRleHQucCA9IDA7CiAgICAgICAgICAgIC8vIOWkhOeQhuWvvOWFpemAiemhuQogICAgICAgICAgICBxdWVzdGlvbnNUb0ltcG9ydCA9ICgwLCBfdG9Db25zdW1hYmxlQXJyYXkyLmRlZmF1bHQpKF90aGlzMTIucGFyc2VkUXVlc3Rpb25zKTsKICAgICAgICAgICAgaWYgKF90aGlzMTIuaW1wb3J0T3B0aW9ucy5yZXZlcnNlKSB7CiAgICAgICAgICAgICAgcXVlc3Rpb25zVG9JbXBvcnQucmV2ZXJzZSgpOwogICAgICAgICAgICB9CgogICAgICAgICAgICAvLyDosIPnlKjlrp7pmYXnmoTlr7zlhaVBUEkKICAgICAgICAgICAgaW1wb3J0RGF0YSA9IHsKICAgICAgICAgICAgICBiYW5rSWQ6IF90aGlzMTIuYmFua0lkLAogICAgICAgICAgICAgIHF1ZXN0aW9uczogcXVlc3Rpb25zVG9JbXBvcnQsCiAgICAgICAgICAgICAgYWxsb3dEdXBsaWNhdGU6IF90aGlzMTIuaW1wb3J0T3B0aW9ucy5hbGxvd0R1cGxpY2F0ZQogICAgICAgICAgICB9OwogICAgICAgICAgICBfY29udGV4dC5uID0gMTsKICAgICAgICAgICAgcmV0dXJuICgwLCBfcXVlc3Rpb25CYW5rLmJhdGNoSW1wb3J0UXVlc3Rpb25zKShpbXBvcnREYXRhKTsKICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dC52OwogICAgICAgICAgICBpZiAoIShyZXNwb25zZS5jb2RlID09PSAyMDApKSB7CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDI7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXMxMi4kbWVzc2FnZS5zdWNjZXNzKCJcdTYyMTBcdTUyOUZcdTVCRkNcdTUxNjUgIi5jb25jYXQocXVlc3Rpb25zVG9JbXBvcnQubGVuZ3RoLCAiIFx1OTA1M1x1OTg5OFx1NzZFRSIpKTsKICAgICAgICAgICAgX2NvbnRleHQubiA9IDM7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKTsKICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgX3RoaXMxMi5pbXBvcnREcmF3ZXJWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMTIuZG9jdW1lbnRDb250ZW50ID0gJyc7CiAgICAgICAgICAgIF90aGlzMTIuZG9jdW1lbnRIdG1sQ29udGVudCA9ICcnOwogICAgICAgICAgICBfdGhpczEyLnBhcnNlZFF1ZXN0aW9ucyA9IFtdOwogICAgICAgICAgICBfdGhpczEyLnBhcnNlRXJyb3JzID0gW107CiAgICAgICAgICAgIF90aGlzMTIuZ2V0UXVlc3Rpb25MaXN0KCk7CiAgICAgICAgICAgIF90aGlzMTIuZ2V0U3RhdGlzdGljcygpOwogICAgICAgICAgICBfY29udGV4dC5uID0gNTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgIF9jb250ZXh0LnAgPSA0OwogICAgICAgICAgICBfdCA9IF9jb250ZXh0LnY7CiAgICAgICAgICAgIF90aGlzMTIuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pScpOwogICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbMCwgNF1dKTsKICAgIH0pKSgpOwogIH0pLCAiaW5pdFJpY2hFZGl0b3IiLCBmdW5jdGlvbiBpbml0UmljaEVkaXRvcigpIHsKICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgIGlmICh0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgIHJldHVybjsKICAgIH0KCiAgICAvLyDmo4Dmn6VDS0VkaXRvcuaYr+WQpuWPr+eUqAogICAgaWYgKCF3aW5kb3cuQ0tFRElUT1IpIHsKICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKTsKICAgICAgcmV0dXJuOwogICAgfQogICAgdHJ5IHsKICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo5bey5a2Y5Zyo77yM5YWI6ZSA5q+BCiAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IpIHsKICAgICAgICB0aGlzLnJpY2hFZGl0b3IuZGVzdHJveSgpOwogICAgICAgIHRoaXMucmljaEVkaXRvciA9IG51bGw7CiAgICAgIH0KCiAgICAgIC8vIOehruS/neWuueWZqOWtmOWcqAogICAgICB2YXIgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJyk7CiAgICAgIGlmICghZWRpdG9yQ29udGFpbmVyKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICAvLyDliJvlu7p0ZXh0YXJlYeWFg+e0oAogICAgICBlZGl0b3JDb250YWluZXIuaW5uZXJIVE1MID0gJzx0ZXh0YXJlYSBpZD0icmljaC1lZGl0b3ItdGV4dGFyZWEiIG5hbWU9InJpY2gtZWRpdG9yLXRleHRhcmVhIj48L3RleHRhcmVhPic7CgogICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7liJvlu7rnvJbovpHlmagKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIC8vIOajgOafpUNLRWRpdG9y5piv5ZCm5Y+v55SoCiAgICAgICAgaWYgKCF3aW5kb3cuQ0tFRElUT1IgfHwgIXdpbmRvdy5DS0VESVRPUi5yZXBsYWNlKSB7CiAgICAgICAgICBfdGhpczEzLnNob3dGYWxsYmFja0VkaXRvciA9IHRydWU7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHRyeSB7CiAgICAgICAgICAvLyDlhYjlsJ3or5XlrozmlbTphY3nva4KICAgICAgICAgIF90aGlzMTMucmljaEVkaXRvciA9IHdpbmRvdy5DS0VESVRPUi5yZXBsYWNlKCdyaWNoLWVkaXRvci10ZXh0YXJlYScsICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKHsKICAgICAgICAgICAgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDIwMHB4KScsCiAgICAgICAgICAgIC8vIOWFqOWxj+mrmOW6puWHj+WOu+WktOmDqOWSjOWFtuS7luWFg+e0oOeahOmrmOW6pgogICAgICAgICAgICB0b29sYmFyOiBbewogICAgICAgICAgICAgIG5hbWU6ICdzdHlsZXMnLAogICAgICAgICAgICAgIGl0ZW1zOiBbJ0ZvbnRTaXplJ10KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIG5hbWU6ICdiYXNpY3N0eWxlcycsCiAgICAgICAgICAgICAgaXRlbXM6IFsnQm9sZCcsICdJdGFsaWMnLCAnVW5kZXJsaW5lJywgJ1N0cmlrZScsICdTdXBlcnNjcmlwdCcsICdTdWJzY3JpcHQnLCAnLScsICdSZW1vdmVGb3JtYXQnXQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbmFtZTogJ2NsaXBib2FyZCcsCiAgICAgICAgICAgICAgaXRlbXM6IFsnQ3V0JywgJ0NvcHknLCAnUGFzdGUnLCAnUGFzdGVUZXh0J10KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIG5hbWU6ICdjb2xvcnMnLAogICAgICAgICAgICAgIGl0ZW1zOiBbJ1RleHRDb2xvcicsICdCR0NvbG9yJ10KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIG5hbWU6ICdwYXJhZ3JhcGgnLAogICAgICAgICAgICAgIGl0ZW1zOiBbJ0p1c3RpZnlMZWZ0JywgJ0p1c3RpZnlDZW50ZXInLCAnSnVzdGlmeVJpZ2h0JywgJ0p1c3RpZnlCbG9jayddCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBuYW1lOiAnZWRpdGluZycsCiAgICAgICAgICAgICAgaXRlbXM6IFsnVW5kbycsICdSZWRvJ10KICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIG5hbWU6ICdsaW5rcycsCiAgICAgICAgICAgICAgaXRlbXM6IFsnTGluaycsICdVbmxpbmsnXQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbmFtZTogJ2luc2VydCcsCiAgICAgICAgICAgICAgaXRlbXM6IFsnSW1hZ2UnLCAnU3BlY2lhbENoYXInXQogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbmFtZTogJ3Rvb2xzJywKICAgICAgICAgICAgICBpdGVtczogWydNYXhpbWl6ZSddCiAgICAgICAgICAgIH1dLAogICAgICAgICAgICByZW1vdmVCdXR0b25zOiAnJywKICAgICAgICAgICAgbGFuZ3VhZ2U6ICd6aC1jbicsCiAgICAgICAgICAgIHJlbW92ZVBsdWdpbnM6ICdlbGVtZW50c3BhdGgnLAogICAgICAgICAgICByZXNpemVfZW5hYmxlZDogZmFsc2UsCiAgICAgICAgICAgIGV4dHJhUGx1Z2luczogJ2ZvbnQsY29sb3JidXR0b24sanVzdGlmeSxzcGVjaWFsY2hhcixpbWFnZScsCiAgICAgICAgICAgIGFsbG93ZWRDb250ZW50OiB0cnVlLAogICAgICAgICAgICAvLyDlrZfkvZPlpKflsI/phY3nva4KICAgICAgICAgICAgZm9udFNpemVfc2l6ZXM6ICcxMi8xMnB4OzE0LzE0cHg7MTYvMTZweDsxOC8xOHB4OzIwLzIwcHg7MjIvMjJweDsyNC8yNHB4OzI2LzI2cHg7MjgvMjhweDszNi8zNnB4OzQ4LzQ4cHg7NzIvNzJweCcsCiAgICAgICAgICAgIGZvbnRTaXplX2RlZmF1bHRMYWJlbDogJzE0cHgnLAogICAgICAgICAgICAvLyDpopzoibLphY3nva4KICAgICAgICAgICAgY29sb3JCdXR0b25fZW5hYmxlTW9yZTogdHJ1ZSwKICAgICAgICAgICAgY29sb3JCdXR0b25fY29sb3JzOiAnQ0Y1RDRFLDQ1NDU0NSxGRkYsQ0NDLERERCxDQ0VBRUUsNjZBQjE2JywKICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgIGZpbGVicm93c2VyVXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy9jb21tb24vdXBsb2FkSW1hZ2UnLAogICAgICAgICAgICBpbWFnZV9wcmV2aWV3VGV4dDogJyAnLAogICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgYmFzZUhyZWY6ICdodHRwOi8vbG9jYWxob3N0Ojg4MDIvJwogICAgICAgICAgfSwgImltYWdlX3ByZXZpZXdUZXh0IiwgJ+mihOiniOWMuuWfnycpLCAiaW1hZ2VfcmVtb3ZlTGlua0J5RW1wdHlVUkwiLCB0cnVlKSwgInJlbW92ZURpYWxvZ1RhYnMiLCAnaW1hZ2U6TGluaztpbWFnZTphZHZhbmNlZCcpLCAib24iLCB7CiAgICAgICAgICAgIHBsdWdpbnNMb2FkZWQ6IGZ1bmN0aW9uIHBsdWdpbnNMb2FkZWQoKSB7fSwKICAgICAgICAgICAgaW5zdGFuY2VSZWFkeTogZnVuY3Rpb24gaW5zdGFuY2VSZWFkeSgpIHsKICAgICAgICAgICAgICB2YXIgZWRpdG9yID0gZXZ0LmVkaXRvcjsKCiAgICAgICAgICAgICAgLy8g566A5Y2V55qE5a+56K+d5qGG5aSE55CGIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5Luj56CB6aOO5qC8CiAgICAgICAgICAgICAgZWRpdG9yLm9uKCdkaWFsb2dTaG93JywgZnVuY3Rpb24gKGV2dCkgewogICAgICAgICAgICAgICAgdmFyIGRpYWxvZyA9IGV2dC5kYXRhOwogICAgICAgICAgICAgICAgaWYgKGRpYWxvZy5nZXROYW1lKCkgPT09ICdpbWFnZScpIHsKICAgICAgICAgICAgICAgICAgLy8g566A5Y2V5qOA5p+l5LiK5Lyg5a6M5oiQ5bm25YiH5o2i5qCH562+6aG1CiAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgIHZhciBjaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgdmFyIHVybEZpZWxkID0gZGlhbG9nLmdldENvbnRlbnRFbGVtZW50KCdpbmZvJywgJ3R4dFVybCcpOwogICAgICAgICAgICAgICAgICAgICAgICBpZiAodXJsRmllbGQgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKSAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpLnN0YXJ0c1dpdGgoJy8nKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7CgogICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWIh+aNouWIsOWbvuWDj+S/oeaBr+agh+etvumhtQogICAgICAgICAgICAgICAgICAgICAgICAgIGRpYWxvZy5zZWxlY3RQYWdlKCdpbmZvJyk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgLy8g5b+955Wl6ZSZ6K+vCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSwgNTAwKTsKCiAgICAgICAgICAgICAgICAgICAgLy8gMTDnp5LlkI7lgZzmraLmo4Dmn6UKICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpOwogICAgICAgICAgICAgICAgICAgIH0sIDEwMDAwKTsKICAgICAgICAgICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pKTsKICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgLy8g5bCd6K+V566A5YyW6YWN572uCiAgICAgICAgICB0cnkgewogICAgICAgICAgICBfdGhpczEzLnJpY2hFZGl0b3IgPSB3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSgncmljaC1lZGl0b3ItdGV4dGFyZWEnLCB7CiAgICAgICAgICAgICAgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDIwMHB4KScsCiAgICAgICAgICAgICAgdG9vbGJhcjogW1snQm9sZCcsICdJdGFsaWMnLCAnVW5kZXJsaW5lJywgJ1N0cmlrZSddLCBbJ051bWJlcmVkTGlzdCcsICdCdWxsZXRlZExpc3QnXSwgWydPdXRkZW50JywgJ0luZGVudCddLCBbJ1VuZG8nLCAnUmVkbyddLCBbJ0xpbmsnLCAnVW5saW5rJ10sIFsnSW1hZ2UnLCAnUmVtb3ZlRm9ybWF0JywgJ01heGltaXplJ11dLAogICAgICAgICAgICAgIHJlbW92ZUJ1dHRvbnM6ICcnLAogICAgICAgICAgICAgIGxhbmd1YWdlOiAnemgtY24nLAogICAgICAgICAgICAgIHJlbW92ZVBsdWdpbnM6ICdlbGVtZW50c3BhdGgnLAogICAgICAgICAgICAgIHJlc2l6ZV9lbmFibGVkOiBmYWxzZSwKICAgICAgICAgICAgICBleHRyYVBsdWdpbnM6ICdpbWFnZScsCiAgICAgICAgICAgICAgYWxsb3dlZENvbnRlbnQ6IHRydWUsCiAgICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgICAgZmlsZWJyb3dzZXJVcGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2NvbW1vbi91cGxvYWRJbWFnZScsCiAgICAgICAgICAgICAgaW1hZ2VfcHJldmlld1RleHQ6ICcgJywKICAgICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgICBiYXNlSHJlZjogJ2h0dHA6Ly9sb2NhbGhvc3Q6ODgwMi8nLAogICAgICAgICAgICAgIC8vIOmakOiXj+S4jemcgOimgeeahOagh+etvumhte+8jOWPquS/neeVmeS4iuS8oOWSjOWbvuWDj+S/oeaBrwogICAgICAgICAgICAgIHJlbW92ZURpYWxvZ1RhYnM6ICdpbWFnZTpMaW5rO2ltYWdlOmFkdmFuY2VkJywKICAgICAgICAgICAgICAvLyDmt7vliqDlrp7kvovlsLHnu6rkuovku7blpITnkIYKICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgaW5zdGFuY2VSZWFkeTogZnVuY3Rpb24gaW5zdGFuY2VSZWFkeShldnQpIHsKICAgICAgICAgICAgICAgICAgdmFyIGVkaXRvciA9IGV2dC5lZGl0b3I7CgogICAgICAgICAgICAgICAgICAvLyDnm5HlkKzlr7nor53moYbmmL7npLrkuovku7YKICAgICAgICAgICAgICAgICAgZWRpdG9yLm9uKCdkaWFsb2dTaG93JywgZnVuY3Rpb24gKGV2dCkgewogICAgICAgICAgICAgICAgICAgIHZhciBkaWFsb2cgPSBldnQuZGF0YTsKICAgICAgICAgICAgICAgICAgICBpZiAoZGlhbG9nLmdldE5hbWUoKSA9PT0gJ2ltYWdlJykgewogICAgICAgICAgICAgICAgICAgICAgLy8g566A5Y2V5qOA5p+l5LiK5Lyg5a6M5oiQ5bm25YiH5o2i5qCH562+6aG1CiAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdmFyIGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhciB1cmxGaWVsZCA9IGRpYWxvZy5nZXRDb250ZW50RWxlbWVudCgnaW5mbycsICd0eHRVcmwnKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh1cmxGaWVsZCAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpICYmIHVybEZpZWxkLmdldFZhbHVlKCkuc3RhcnRzV2l0aCgnLycpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDliIfmjaLliLDlm77lg4/kv6Hmga/moIfnrb7pobUKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDlv73nlaXplJnor68KICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0sIDUwMCk7CgogICAgICAgICAgICAgICAgICAgICAgICAvLyAxMOenkuWQjuWBnOatouajgOafpQogICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKTsKICAgICAgICAgICAgICAgICAgICAgICAgfSwgMTAwMDApOwogICAgICAgICAgICAgICAgICAgICAgfSwgMTAwMCk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikgewogICAgICAgICAgICBfdGhpczEzLnNob3dGYWxsYmFja0VkaXRvciA9IHRydWU7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOebkeWQrOWGheWuueWPmOWMlgogICAgICAgIGlmIChfdGhpczEzLnJpY2hFZGl0b3IgJiYgX3RoaXMxMy5yaWNoRWRpdG9yLm9uKSB7CiAgICAgICAgICBfdGhpczEzLnJpY2hFZGl0b3Iub24oJ2NoYW5nZScsIGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgdmFyIHJhd0NvbnRlbnQgPSBfdGhpczEzLnJpY2hFZGl0b3IuZ2V0RGF0YSgpOwogICAgICAgICAgICB2YXIgY29udGVudFdpdGhSZWxhdGl2ZVVybHMgPSBfdGhpczEzLmNvbnZlcnRVcmxzVG9SZWxhdGl2ZShyYXdDb250ZW50KTsKCiAgICAgICAgICAgIC8vIOS/neWtmEhUTUzlhoXlrrnnlKjkuo7pooTop4gKICAgICAgICAgICAgX3RoaXMxMy5kb2N1bWVudEh0bWxDb250ZW50ID0gX3RoaXMxMy5wcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyhjb250ZW50V2l0aFJlbGF0aXZlVXJscyk7CiAgICAgICAgICAgIC8vIOS/neWtmOe6r+aWh+acrOWGheWuueeUqOS6juino+aekAogICAgICAgICAgICBfdGhpczEzLmRvY3VtZW50Q29udGVudCA9IF90aGlzMTMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpOwogICAgICAgICAgfSk7CiAgICAgICAgfQoKICAgICAgICAvLyDnm5HlkKzmjInplK7kuovku7YKICAgICAgICBfdGhpczEzLnJpY2hFZGl0b3Iub24oJ2tleScsIGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICB2YXIgcmF3Q29udGVudCA9IF90aGlzMTMucmljaEVkaXRvci5nZXREYXRhKCk7CiAgICAgICAgICAgIHZhciBjb250ZW50V2l0aFJlbGF0aXZlVXJscyA9IF90aGlzMTMuY29udmVydFVybHNUb1JlbGF0aXZlKHJhd0NvbnRlbnQpOwoKICAgICAgICAgICAgLy8g5L+d5a2YSFRNTOWGheWuueeUqOS6jumihOiniAogICAgICAgICAgICBfdGhpczEzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBfdGhpczEzLnByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nKGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzKTsKICAgICAgICAgICAgLy8g5L+d5a2Y57qv5paH5pys5YaF5a6555So5LqO6Kej5p6QCiAgICAgICAgICAgIF90aGlzMTMuZG9jdW1lbnRDb250ZW50ID0gX3RoaXMxMy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50V2l0aFJlbGF0aXZlVXJscyk7CiAgICAgICAgICB9LCAxMDApOwogICAgICAgIH0pOwoKICAgICAgICAvLyDnm5HlkKzlrp7kvovlh4blpIflsLHnu6oKICAgICAgICBfdGhpczEzLnJpY2hFZGl0b3Iub24oJ2luc3RhbmNlUmVhZHknLCBmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczEzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZTsKICAgICAgICAgIC8vIOe8lui+keWZqOWIneWni+WMluWujOaIkO+8jOehruS/neWGheWuueS4uuepugogICAgICAgICAgX3RoaXMxMy5yaWNoRWRpdG9yLnNldERhdGEoJycpOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgIC8vIOWmguaenENLRWRpdG9y5Yid5aeL5YyW5aSx6LSl77yM5Zue6YCA5Yiw5pmu6YCa5paH5pys5qGGCiAgICAgIHRoaXMuZmFsbGJhY2tUb1RleHRhcmVhKCk7CiAgICB9CiAgfSksICJmYWxsYmFja1RvVGV4dGFyZWEiLCBmdW5jdGlvbiBmYWxsYmFja1RvVGV4dGFyZWEoKSB7CiAgICB2YXIgX3RoaXMxNCA9IHRoaXM7CiAgICB2YXIgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJyk7CiAgICBpZiAoZWRpdG9yQ29udGFpbmVyKSB7CiAgICAgIHZhciB0ZXh0YXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJyk7CiAgICAgIHRleHRhcmVhLmNsYXNzTmFtZSA9ICdmYWxsYmFjay10ZXh0YXJlYSc7CiAgICAgIHRleHRhcmVhLnBsYWNlaG9sZGVyID0gJ+ivt+WcqOatpOWkhOeymOi0tOaIlui+k+WFpemimOebruWGheWuuS4uLic7CiAgICAgIHRleHRhcmVhLnZhbHVlID0gJyc7IC8vIOehruS/neaWh+acrOahhuS4uuepugogICAgICB0ZXh0YXJlYS5zdHlsZS5jc3NUZXh0ID0gJ3dpZHRoOiAxMDAlOyBoZWlnaHQ6IDQwMHB4OyBib3JkZXI6IDFweCBzb2xpZCAjZGRkOyBwYWRkaW5nOiAxMHB4OyBmb250LWZhbWlseTogIkNvdXJpZXIgTmV3IiwgbW9ub3NwYWNlOyBmb250LXNpemU6IDE0cHg7IGxpbmUtaGVpZ2h0OiAxLjY7IHJlc2l6ZTogbm9uZTsnOwoKICAgICAgLy8g55uR5ZCs5YaF5a655Y+Y5YyWCiAgICAgIHRleHRhcmVhLmFkZEV2ZW50TGlzdGVuZXIoJ2lucHV0JywgZnVuY3Rpb24gKGUpIHsKICAgICAgICBfdGhpczE0LmRvY3VtZW50Q29udGVudCA9IGUudGFyZ2V0LnZhbHVlOwogICAgICAgIF90aGlzMTQuZG9jdW1lbnRIdG1sQ29udGVudCA9IGUudGFyZ2V0LnZhbHVlOyAvLyDnuq/mlofmnKzmqKHlvI/kuItIVE1M5YaF5a655LiO5paH5pys5YaF5a6555u45ZCMCiAgICAgIH0pOwogICAgICBlZGl0b3JDb250YWluZXIuaW5uZXJIVE1MID0gJyc7CiAgICAgIGVkaXRvckNvbnRhaW5lci5hcHBlbmRDaGlsZCh0ZXh0YXJlYSk7CiAgICAgIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQgPSB0cnVlOwogICAgfQogIH0pLCAic3RyaXBIdG1sVGFncyIsIGZ1bmN0aW9uIHN0cmlwSHRtbFRhZ3MoaHRtbCkgewogICAgdmFyIGRpdiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpOwogICAgZGl2LmlubmVySFRNTCA9IGh0bWw7CiAgICByZXR1cm4gZGl2LnRleHRDb250ZW50IHx8IGRpdi5pbm5lclRleHQgfHwgJyc7CiAgfSksICJzZXRFZGl0b3JDb250ZW50IiwgZnVuY3Rpb24gc2V0RWRpdG9yQ29udGVudChjb250ZW50KSB7CiAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMuZWRpdG9ySW5pdGlhbGl6ZWQpIHsKICAgICAgdGhpcy5yaWNoRWRpdG9yLnNldERhdGEoY29udGVudCk7CiAgICB9IGVsc2UgewogICAgICAvLyDlpoLmnpznvJbovpHlmajov5jmnKrliJ3lp4vljJbvvIzkv53lrZjlhoXlrrnnrYnlvoXliJ3lp4vljJblrozmiJDlkI7orr7nva4KICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBjb250ZW50OwogICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBjb250ZW50OyAvLyDlkIzml7borr7nva5IVE1M5YaF5a65CiAgICB9CiAgfSksICJkZWJvdW5jZSIsIGZ1bmN0aW9uIGRlYm91bmNlKGZ1bmMsIHdhaXQpIHsKICAgIHZhciB0aW1lb3V0OwogICAgcmV0dXJuIGZ1bmN0aW9uIGV4ZWN1dGVkRnVuY3Rpb24oKSB7CiAgICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykgewogICAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07CiAgICAgIH0KICAgICAgdmFyIGxhdGVyID0gZnVuY3Rpb24gbGF0ZXIoKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpOwogICAgICAgIGZ1bmMuYXBwbHkodm9pZCAwLCBhcmdzKTsKICAgICAgfTsKICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpOwogICAgICB0aW1lb3V0ID0gc2V0VGltZW91dChsYXRlciwgd2FpdCk7CiAgICB9OwogIH0pLCAiY29udmVydFVybHNUb1JlbGF0aXZlIiwgZnVuY3Rpb24gY29udmVydFVybHNUb1JlbGF0aXZlKGNvbnRlbnQpIHsKICAgIGlmICghY29udGVudCkgcmV0dXJuIGNvbnRlbnQ7CgogICAgLy8g5Yy56YWN5b2T5YmN5Z+f5ZCN55qE5a6M5pW0VVJM5bm26L2s5o2i5Li655u45a+56Lev5b6ECiAgICB2YXIgY3VycmVudE9yaWdpbiA9IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW47CiAgICB2YXIgdXJsUmVnZXggPSBuZXcgUmVnRXhwKGN1cnJlbnRPcmlnaW4ucmVwbGFjZSgvWy4qKz9eJHt9KCl8W1xdXFxdL2csICdcXCQmJykgKyAnKC9bXiJcJ1xccz5dKiknLCAnZycpOwogICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSh1cmxSZWdleCwgJyQxJyk7CiAgfSksICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9tZXRob2RzLCAicGFyc2VEb2N1bWVudCIsIGZ1bmN0aW9uIHBhcnNlRG9jdW1lbnQoKSB7CiAgICBpZiAoIXRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKSkgewogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdOwogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW107CiAgICAgIHJldHVybjsKICAgIH0KICAgIHRyeSB7CiAgICAgIHZhciBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQodGhpcy5kb2N1bWVudENvbnRlbnQpOwogICAgICAvLyDkuLrmr4/kuKrpopjnm67mt7vliqBjb2xsYXBzZWTlsZ7mgKcKICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBwYXJzZVJlc3VsdC5xdWVzdGlvbnMubWFwKGZ1bmN0aW9uIChxdWVzdGlvbikgewogICAgICAgIHJldHVybiAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBxdWVzdGlvbiksIHt9LCB7CiAgICAgICAgICBjb2xsYXBzZWQ6IGZhbHNlCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcGFyc2VSZXN1bHQuZXJyb3JzOwogICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFsn6Kej5p6Q5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2VdOwogICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdOwogICAgfQogIH0pLCAicGFyc2VRdWVzdGlvbkNvbnRlbnQiLCBmdW5jdGlvbiBwYXJzZVF1ZXN0aW9uQ29udGVudChjb250ZW50KSB7CiAgICB2YXIgcXVlc3Rpb25zID0gW107CiAgICB2YXIgZXJyb3JzID0gW107CiAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgcXVlc3Rpb25zOiBxdWVzdGlvbnMsCiAgICAgICAgZXJyb3JzOiBbJ+ino+aekOWGheWuueS4uuepuuaIluagvOW8j+S4jeato+ehriddCiAgICAgIH07CiAgICB9CiAgICB0cnkgewogICAgICAvLyDkv53nlZnlm77niYfmoIfnrb7vvIzlj6rnp7vpmaTlhbbku5ZIVE1M5qCH562+CiAgICAgIHZhciB0ZXh0Q29udGVudCA9IHRoaXMuc3RyaXBIdG1sVGFnc0tlZXBJbWFnZXMoY29udGVudCk7CiAgICAgIGlmICghdGV4dENvbnRlbnQgfHwgdGV4dENvbnRlbnQudHJpbSgpLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBxdWVzdGlvbnM6IHF1ZXN0aW9ucywKICAgICAgICAgIGVycm9yczogWyflpITnkIblkI7nmoTlhoXlrrnkuLrnqbonXQogICAgICAgIH07CiAgICAgIH0KCiAgICAgIC8vIOaMieihjOWIhuWJsuWGheWuuQogICAgICB2YXIgbGluZXMgPSB0ZXh0Q29udGVudC5zcGxpdCgnXG4nKS5tYXAoZnVuY3Rpb24gKGxpbmUpIHsKICAgICAgICByZXR1cm4gbGluZS50cmltKCk7CiAgICAgIH0pLmZpbHRlcihmdW5jdGlvbiAobGluZSkgewogICAgICAgIHJldHVybiBsaW5lLmxlbmd0aCA+IDA7CiAgICAgIH0pOwogICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIHF1ZXN0aW9uczogcXVlc3Rpb25zLAogICAgICAgICAgZXJyb3JzOiBbJ+ayoeacieacieaViOeahOWGheWuueihjCddCiAgICAgICAgfTsKICAgICAgfQogICAgICB2YXIgY3VycmVudFF1ZXN0aW9uTGluZXMgPSBbXTsKICAgICAgdmFyIHF1ZXN0aW9uTnVtYmVyID0gMDsKICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBsaW5lID0gbGluZXNbaV07CgogICAgICAgIC8vIOajgOafpeaYr+WQpuaYr+mimOebruW8gOWni+ihjO+8muaVsOWtl+OAgVvpopjnm67nsbvlnotdIOaIliBb6aKY55uu57G75Z6LXQogICAgICAgIHZhciBpc1F1ZXN0aW9uU3RhcnQgPSB0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUobGluZSkgfHwgdGhpcy5pc1F1ZXN0aW9uVHlwZVN0YXJ0KGxpbmUpOwogICAgICAgIGlmIChpc1F1ZXN0aW9uU3RhcnQpIHsKICAgICAgICAgIC8vIOWmguaenOS5i+WJjeaciemimOebruWGheWuue+8jOWFiOWkhOeQhuS5i+WJjeeahOmimOebrgogICAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICB2YXIgcXVlc3Rpb25UZXh0ID0gY3VycmVudFF1ZXN0aW9uTGluZXMuam9pbignXG4nKTsKICAgICAgICAgICAgICB2YXIgcGFyc2VkUXVlc3Rpb24gPSB0aGlzLnBhcnNlUXVlc3Rpb25Gcm9tTGluZXMocXVlc3Rpb25UZXh0LCBxdWVzdGlvbk51bWJlcik7CiAgICAgICAgICAgICAgaWYgKHBhcnNlZFF1ZXN0aW9uKSB7CiAgICAgICAgICAgICAgICBxdWVzdGlvbnMucHVzaChwYXJzZWRRdWVzdGlvbik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgIGVycm9ycy5wdXNoKCJcdTdCMkMgIi5jb25jYXQocXVlc3Rpb25OdW1iZXIsICIgXHU5ODk4XHU4OUUzXHU2NzkwXHU1OTMxXHU4RDI1OiAiKS5jb25jYXQoZXJyb3IubWVzc2FnZSkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgLy8g5byA5aeL5paw6aKY55uuCiAgICAgICAgICBjdXJyZW50UXVlc3Rpb25MaW5lcyA9IFtsaW5lXTsKICAgICAgICAgIHF1ZXN0aW9uTnVtYmVyKys7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOWmguaenOW9k+WJjeWcqOWkhOeQhumimOebruS4re+8jOa3u+WKoOWIsOW9k+WJjemimOebrgogICAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgY3VycmVudFF1ZXN0aW9uTGluZXMucHVzaChsaW5lKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhuacgOWQjuS4gOS4qumimOebrgogICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgIHRyeSB7CiAgICAgICAgICB2YXIgX3F1ZXN0aW9uVGV4dCA9IGN1cnJlbnRRdWVzdGlvbkxpbmVzLmpvaW4oJ1xuJyk7CiAgICAgICAgICB2YXIgX3BhcnNlZFF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKF9xdWVzdGlvblRleHQsIHF1ZXN0aW9uTnVtYmVyKTsKICAgICAgICAgIGlmIChfcGFyc2VkUXVlc3Rpb24pIHsKICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2goX3BhcnNlZFF1ZXN0aW9uKTsKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgZXJyb3JzLnB1c2goIlx1N0IyQyAiLmNvbmNhdChxdWVzdGlvbk51bWJlciwgIiBcdTk4OThcdTg5RTNcdTY3OTBcdTU5MzFcdThEMjU6ICIpLmNvbmNhdChlcnJvci5tZXNzYWdlKSk7CiAgICAgICAgfQogICAgICB9CiAgICB9IGNhdGNoIChlcnJvcikgewogICAgICBlcnJvcnMucHVzaCgiXHU2NTg3XHU2ODYzXHU4OUUzXHU2NzkwXHU1OTMxXHU4RDI1OiAiLmNvbmNhdChlcnJvci5tZXNzYWdlKSk7CiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmlofmoaPop6PmnpDlpLHotKU6JywgZXJyb3IpOwogICAgfQogICAgY29uc29sZS5sb2coJ+ino+aekOWujOaIkO+8jOWFsScsIHF1ZXN0aW9ucy5sZW5ndGgsICfpgZPpopjnm67vvIwnLCBlcnJvcnMubGVuZ3RoLCAn5Liq6ZSZ6K+vJyk7CiAgICByZXR1cm4gewogICAgICBxdWVzdGlvbnM6IHF1ZXN0aW9ucywKICAgICAgZXJyb3JzOiBlcnJvcnMKICAgIH07CiAgfSksICJpc1F1ZXN0aW9uU3RhcnRMaW5lIiwgZnVuY3Rpb24gaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSB7CiAgICAvLyDop4TojIPvvJrmr4/popjliY3pnaLpnIDopoHliqDkuIrpopjlj7fmoIfor4bvvIzpopjlj7flkI7pnaLpnIDopoHliqDkuIrnrKblj7fvvIg677ya44CBLu+8ju+8iQogICAgLy8g5Yy56YWN5qC85byP77ya5pWw5a2XICsg56ym5Y+3KDrvvJrjgIEu77yOKSArIOWPr+mAieepuuagvAogICAgLy8g5L6L5aaC77yaMS4gMeOAgSAx77yaIDHvvI4g562JCiAgICByZXR1cm4gL15cZCtbLjrvvJrvvI7jgIFdXHMqLy50ZXN0KGxpbmUpOwogIH0pLCAiaXNRdWVzdGlvblR5cGVTdGFydCIsIGZ1bmN0aW9uIGlzUXVlc3Rpb25UeXBlU3RhcnQobGluZSkgewogICAgLy8g5Yy56YWN5qC85byP77yaW+mimOebruexu+Wei10KICAgIC8vIOS+i+Wmgu+8mlvljZXpgInpophdIFvlpJrpgInpophdIFvliKTmlq3pophdIOetiQogICAgcmV0dXJuIC9eXFsuKj/pophcXS8udGVzdChsaW5lKTsKICB9KSwgInBhcnNlUXVlc3Rpb25Gcm9tTGluZXMiLCBmdW5jdGlvbiBwYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCkgewogICAgdmFyIGxpbmVzID0gcXVlc3Rpb25UZXh0LnNwbGl0KCdcbicpLm1hcChmdW5jdGlvbiAobGluZSkgewogICAgICByZXR1cm4gbGluZS50cmltKCk7CiAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKGxpbmUpIHsKICAgICAgcmV0dXJuIGxpbmUubGVuZ3RoID4gMDsKICAgIH0pOwogICAgaWYgKGxpbmVzLmxlbmd0aCA9PT0gMCkgewogICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOebruWGheWuueS4uuepuicpOwogICAgfQogICAgdmFyIHF1ZXN0aW9uVHlwZSA9ICdqdWRnbWVudCc7IC8vIOm7mOiupOWIpOaWremimAogICAgdmFyIHF1ZXN0aW9uQ29udGVudCA9ICcnOwogICAgdmFyIGNvbnRlbnRTdGFydEluZGV4ID0gMDsKCiAgICAvLyDmo4Dmn6XmmK/lkKbmnInpopjlnovmoIfms6jvvIjlpoIgW+WNlemAiemimF3jgIFb5aSa6YCJ6aKYXeOAgVvliKTmlq3pophd77yJCiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgIHZhciBsaW5lID0gbGluZXNbaV07CiAgICAgIHZhciB0eXBlTWF0Y2ggPSBsaW5lLm1hdGNoKC9cWyguKj/popgpXF0vKTsKICAgICAgaWYgKHR5cGVNYXRjaCkgewogICAgICAgIHZhciB0eXBlVGV4dCA9IHR5cGVNYXRjaFsxXTsKCiAgICAgICAgLy8g6L2s5o2i6aKY55uu57G75Z6LCiAgICAgICAgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfliKTmlq0nKSkgewogICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2p1ZGdtZW50JzsKICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfljZXpgIknKSkgewogICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ3NpbmdsZSc7CiAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aSa6YCJJykpIHsKICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZSc7CiAgICAgICAgfSBlbHNlIGlmICh0eXBlVGV4dC5pbmNsdWRlcygn5aGr56m6JykpIHsKICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdmaWxsJzsKICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfnroDnrZQnKSkgewogICAgICAgICAgcXVlc3Rpb25UeXBlID0gJ2Vzc2F5JzsKICAgICAgICB9CgogICAgICAgIC8vIOWmguaenOmimOWei+agh+azqOWSjOmimOebruWGheWuueWcqOWQjOS4gOihjAogICAgICAgIHZhciByZW1haW5pbmdDb250ZW50ID0gbGluZS5yZXBsYWNlKC9cWy4qP+mimFxdLywgJycpLnRyaW0oKTsKICAgICAgICBpZiAocmVtYWluaW5nQ29udGVudCkgewogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gcmVtYWluaW5nQ29udGVudDsKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDE7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDE7CiAgICAgICAgfQogICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9CgogICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw6aKY5Z6L5qCH5rOo77yM5LuO56ys5LiA6KGM5byA5aeL6Kej5p6QCiAgICBpZiAoY29udGVudFN0YXJ0SW5kZXggPT09IDApIHsKICAgICAgY29udGVudFN0YXJ0SW5kZXggPSAwOwogICAgfQoKICAgIC8vIOaPkOWPlumimOebruWGheWuue+8iOS7jumimOWPt+ihjOW8gOWni++8iQogICAgZm9yICh2YXIgX2kgPSBjb250ZW50U3RhcnRJbmRleDsgX2kgPCBsaW5lcy5sZW5ndGg7IF9pKyspIHsKICAgICAgdmFyIF9saW5lID0gbGluZXNbX2ldOwoKICAgICAgLy8g5aaC5p6c5piv6aKY5Y+36KGM77yM5o+Q5Y+W6aKY55uu5YaF5a6577yI56e76Zmk6aKY5Y+377yJCiAgICAgIGlmICh0aGlzLmlzUXVlc3Rpb25TdGFydExpbmUoX2xpbmUpKSB7CiAgICAgICAgLy8g56e76Zmk6aKY5Y+377yM5o+Q5Y+W6aKY55uu5YaF5a65CiAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gX2xpbmUucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpOwogICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gX2kgKyAxOwogICAgICAgIGJyZWFrOwogICAgICB9IGVsc2UgaWYgKCFxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICAvLyDlpoLmnpzov5jmsqHmnInpopjnm67lhoXlrrnvvIzlvZPliY3ooYzlsLHmmK/popjnm67lhoXlrrkKICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBfbGluZTsKICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IF9pICsgMTsKICAgICAgICBicmVhazsKICAgICAgfQogICAgfQoKICAgIC8vIOe7p+e7reaUtumbhumimOebruWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhueaIluetlOahiO+8iQogICAgZm9yICh2YXIgX2kyID0gY29udGVudFN0YXJ0SW5kZXg7IF9pMiA8IGxpbmVzLmxlbmd0aDsgX2kyKyspIHsKICAgICAgdmFyIF9saW5lMiA9IGxpbmVzW19pMl07CgogICAgICAvLyDlpoLmnpzpgYfliLDpgInpobnooYzjgIHnrZTmoYjooYzjgIHop6PmnpDooYzmiJbpmr7luqbooYzvvIzlgZzmraLmlLbpm4bpopjnm67lhoXlrrkKICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKF9saW5lMikgfHwgdGhpcy5pc0Fuc3dlckxpbmUoX2xpbmUyKSB8fCB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKF9saW5lMikgfHwgdGhpcy5pc0RpZmZpY3VsdHlMaW5lKF9saW5lMikpIHsKICAgICAgICBicmVhazsKICAgICAgfQoKICAgICAgLy8g57un57ut5re75Yqg5Yiw6aKY55uu5YaF5a6577yM5L2G6KaB56Gu5L+d5LiN5YyF5ZCr6aKY5Y+3CiAgICAgIHZhciBjbGVhbkxpbmUgPSBfbGluZTI7CiAgICAgIC8vIOWmguaenOi/meihjOi/mOWMheWQq+mimOWPt++8jOenu+mZpOWugwogICAgICBpZiAodGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKF9saW5lMikpIHsKICAgICAgICBjbGVhbkxpbmUgPSBfbGluZTIucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpOwogICAgICB9CiAgICAgIGlmIChjbGVhbkxpbmUpIHsKICAgICAgICBpZiAocXVlc3Rpb25Db250ZW50KSB7CiAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgKz0gJ1xuJyArIGNsZWFuTGluZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcXVlc3Rpb25Db250ZW50ID0gY2xlYW5MaW5lOwogICAgICAgIH0KICAgICAgfQogICAgfQogICAgaWYgKCFxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xmj5Dlj5bpopjnm67lhoXlrrknKTsKICAgIH0KCiAgICAvLyDmnIDnu4jmuIXnkIbvvJrnoa7kv53popjnm67lhoXlrrnkuI3ljIXlkKvpopjlj7cKICAgIHZhciBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IHF1ZXN0aW9uQ29udGVudC50cmltKCk7CiAgICAvLyDkvb/nlKjmm7TlvLrnmoTmuIXnkIbpgLvovpHvvIzlpJrmrKHmuIXnkIbnoa7kv53lvbvlupXnp7vpmaTpopjlj7cKICAgIHdoaWxlICgvXlxzKlxkK1suOu+8mu+8juOAgV0vLnRlc3QoZmluYWxRdWVzdGlvbkNvbnRlbnQpKSB7CiAgICAgIGZpbmFsUXVlc3Rpb25Db250ZW50ID0gZmluYWxRdWVzdGlvbkNvbnRlbnQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpOwogICAgfQoKICAgIC8vIOmineWklua4heeQhu+8muenu+mZpOWPr+iDveeahEhUTUzmoIfnrb7lhoXnmoTpopjlj7cKICAgIGlmIChmaW5hbFF1ZXN0aW9uQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgIGZpbmFsUXVlc3Rpb25Db250ZW50ID0gdGhpcy5yZW1vdmVRdWVzdGlvbk51bWJlcihmaW5hbFF1ZXN0aW9uQ29udGVudCk7CiAgICB9CiAgICB2YXIgcXVlc3Rpb24gPSB7CiAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICB0eXBlOiBxdWVzdGlvblR5cGUsCiAgICAgIHR5cGVOYW1lOiB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpLAogICAgICBxdWVzdGlvbkNvbnRlbnQ6IGZpbmFsUXVlc3Rpb25Db250ZW50LAogICAgICBjb250ZW50OiBmaW5hbFF1ZXN0aW9uQ29udGVudCwKICAgICAgZGlmZmljdWx0eTogJycsCiAgICAgIC8vIOS4jeiuvue9rum7mOiupOWAvAogICAgICBleHBsYW5hdGlvbjogJycsCiAgICAgIG9wdGlvbnM6IFtdLAogICAgICBjb3JyZWN0QW5zd2VyOiAnJywKICAgICAgY29sbGFwc2VkOiBmYWxzZSAvLyDpu5jorqTlsZXlvIAKICAgIH07CgogICAgLy8g6Kej5p6Q6YCJ6aG577yI5a+55LqO6YCJ5oup6aKY77yJCiAgICB2YXIgb3B0aW9uUmVzdWx0ID0gdGhpcy5wYXJzZU9wdGlvbnNGcm9tTGluZXMobGluZXMsIDApOwogICAgcXVlc3Rpb24ub3B0aW9ucyA9IG9wdGlvblJlc3VsdC5vcHRpb25zOwoKICAgIC8vIOagueaNrumAiemhueaVsOmHj+aOqOaWremimOebruexu+Wei++8iOWmguaenOS5i+WJjeayoeacieaYjuehruagh+azqO+8iQogICAgaWYgKHF1ZXN0aW9uVHlwZSA9PT0gJ2p1ZGdtZW50JyAmJiBxdWVzdGlvbi5vcHRpb25zLmxlbmd0aCA+IDApIHsKICAgICAgLy8g5aaC5p6c5pyJ6YCJ6aG577yM5o6o5pat5Li66YCJ5oup6aKYCiAgICAgIHF1ZXN0aW9uVHlwZSA9ICdzaW5nbGUnOyAvLyDpu5jorqTkuLrljZXpgInpopgKICAgICAgcXVlc3Rpb24ucXVlc3Rpb25UeXBlID0gcXVlc3Rpb25UeXBlOwogICAgICBxdWVzdGlvbi50eXBlID0gcXVlc3Rpb25UeXBlOwogICAgICBxdWVzdGlvbi50eXBlTmFtZSA9IHRoaXMuZ2V0VHlwZURpc3BsYXlOYW1lKHF1ZXN0aW9uVHlwZSk7CiAgICB9CgogICAgLy8g6Kej5p6Q562U5qGI44CB6Kej5p6Q44CB6Zq+5bqmCiAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhRnJvbUxpbmVzKGxpbmVzLCBxdWVzdGlvbik7CgogICAgLy8g5qC55o2u562U5qGI6ZW/5bqm6L+b5LiA5q2l5o6o5pat6YCJ5oup6aKY57G75Z6LCiAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJyAmJiBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyICYmIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIubGVuZ3RoID4gMSkgewogICAgICAvLyDlpoLmnpznrZTmoYjljIXlkKvlpJrkuKrlrZfmr43vvIzmjqjmlq3kuLrlpJrpgInpopgKICAgICAgaWYgKC9eW0EtWl17Mix9JC8udGVzdChxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSkgewogICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdtdWx0aXBsZSc7CiAgICAgICAgcXVlc3Rpb24ucXVlc3Rpb25UeXBlID0gcXVlc3Rpb25UeXBlOwogICAgICAgIHF1ZXN0aW9uLnR5cGUgPSBxdWVzdGlvblR5cGU7CiAgICAgICAgcXVlc3Rpb24udHlwZU5hbWUgPSB0aGlzLmdldFR5cGVEaXNwbGF5TmFtZShxdWVzdGlvblR5cGUpOwogICAgICB9CiAgICB9CgogICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655a6M5YWo5rKh5pyJ6aKY5Y+3CiAgICBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCk7CiAgICBxdWVzdGlvbi5jb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50OwogICAgcmV0dXJuIHF1ZXN0aW9uOwogIH0pLCAiaXNPcHRpb25MaW5lIiwgZnVuY3Rpb24gaXNPcHRpb25MaW5lKGxpbmUpIHsKICAgIC8vIOinhOiMg++8mumAiemhueagvOW8j++8iEE677yJ77yM5a2X5q+N5Y+v5Lul5Li6QeWIsFrnmoTku7vmhI/lpKflsI/lhpnlrZfmr43vvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLoiOu+8muOAgS7vvI4i5YW25Lit5LmL5LiACiAgICByZXR1cm4gL15bQS1aYS16XVsuOu+8mu+8juOAgV1ccyovLnRlc3QobGluZSk7CiAgfSksICJpc0Fuc3dlckxpbmUiLCBmdW5jdGlvbiBpc0Fuc3dlckxpbmUobGluZSkgewogICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgcmV0dXJuIC9e562U5qGIWy4677ya44CBXVxzKi8udGVzdChsaW5lKTsKICB9KSwgImlzRXhwbGFuYXRpb25MaW5lIiwgZnVuY3Rpb24gaXNFeHBsYW5hdGlvbkxpbmUobGluZSkgewogICAgLy8g6KeE6IyD77ya6Kej5p6Q5qC85byP77yI6Kej5p6Q77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgcmV0dXJuIC9e6Kej5p6QWy4677ya44CBXVxzKi8udGVzdChsaW5lKTsKICB9KSwgImlzRGlmZmljdWx0eUxpbmUiLCBmdW5jdGlvbiBpc0RpZmZpY3VsdHlMaW5lKGxpbmUpIHsKICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgIHJldHVybiAvXumavuW6plsuOu+8muOAgV1ccyovLnRlc3QobGluZSk7CiAgfSksICJnZXRUeXBlRGlzcGxheU5hbWUiLCBmdW5jdGlvbiBnZXRUeXBlRGlzcGxheU5hbWUodHlwZSkgewogICAgdmFyIHR5cGVNYXAgPSB7CiAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnLAogICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAnZmlsbCc6ICfloavnqbrpopgnLAogICAgICAnZXNzYXknOiAn566A562U6aKYJwogICAgfTsKICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfliKTmlq3popgnOwogIH0pLCAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShfbWV0aG9kcywgInByb2Nlc3NJbWFnZVBhdGhzIiwgZnVuY3Rpb24gcHJvY2Vzc0ltYWdlUGF0aHMoY29udGVudCkgewogICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICByZXR1cm4gJyc7CiAgICB9CiAgICB0cnkgewogICAgICAvLyDlpITnkIZpbWfmoIfnrb7kuK3nmoTnm7jlr7not6/lvoQKICAgICAgdmFyIHByb2Nlc3NlZENvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoLzxpbWcoW14+XSo/KXNyYz0iKFteIl0qPykiKFtePl0qPyk+L2csIGZ1bmN0aW9uIChtYXRjaCwgYmVmb3JlLCBzcmMsIGFmdGVyKSB7CiAgICAgICAgaWYgKCFzcmMpIHJldHVybiBtYXRjaDsKCiAgICAgICAgLy8g5aaC5p6c5bey57uP5piv5a6M5pW06Lev5b6E77yM5LiN5aSE55CGCiAgICAgICAgaWYgKHNyYy5zdGFydHNXaXRoKCdodHRwOi8vJykgfHwgc3JjLnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykgfHwgc3JjLnN0YXJ0c1dpdGgoJ2RhdGE6JykpIHsKICAgICAgICAgIHJldHVybiBtYXRjaDsKICAgICAgICB9CgogICAgICAgIC8vIOWmguaenOaYr+ebuOWvuei3r+W+hO+8jOa3u+WKoOWQjuerr+acjeWKoeWZqOWcsOWdgAogICAgICAgIHZhciBmdWxsU3JjID0gJ2h0dHA6Ly9sb2NhbGhvc3Q6ODgwMicgKyAoc3JjLnN0YXJ0c1dpdGgoJy8nKSA/IHNyYyA6ICcvJyArIHNyYyk7CiAgICAgICAgdmFyIHJlc3VsdCA9ICI8aW1nIi5jb25jYXQoYmVmb3JlLCAic3JjPVwiIikuY29uY2F0KGZ1bGxTcmMsICJcIiIpLmNvbmNhdChhZnRlciwgIj4iKTsKICAgICAgICByZXR1cm4gcmVzdWx0OwogICAgICB9KTsKICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQ7CiAgICB9IGNhdGNoIChlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCfinYwg5aSE55CG5Zu+54mH6Lev5b6E5pe25Ye66ZSZOicsIGVycm9yKTsKICAgICAgcmV0dXJuIGNvbnRlbnQ7CiAgICB9CiAgfSksICJwcmVzZXJ2ZVJpY2hUZXh0Rm9ybWF0dGluZyIsIGZ1bmN0aW9uIHByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nKGNvbnRlbnQpIHsKICAgIHZhciBfdGhpczE1ID0gdGhpczsKICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgcmV0dXJuICcnOwogICAgfQogICAgdHJ5IHsKICAgICAgLy8g5L+d55WZ5bi455So55qE5a+M5paH5pys5qC85byP5qCH562+CiAgICAgIHZhciBwcm9jZXNzZWRDb250ZW50ID0gY29udGVudAogICAgICAvLyDovazmjaLnm7jlr7not6/lvoTnmoTlm77niYcKICAgICAgLnJlcGxhY2UoLzxpbWcoW14+XSo/KXNyYz0iKFteIl0qPykiKFtePl0qPyk+L2dpLCBmdW5jdGlvbiAobWF0Y2gsIGJlZm9yZSwgc3JjLCBhZnRlcikgewogICAgICAgIGlmICghc3JjLnN0YXJ0c1dpdGgoJ2h0dHAnKSAmJiAhc3JjLnN0YXJ0c1dpdGgoJ2RhdGE6JykpIHsKICAgICAgICAgIHZhciBmdWxsU3JjID0gX3RoaXMxNS5wcm9jZXNzSW1hZ2VQYXRocyhzcmMpOwogICAgICAgICAgcmV0dXJuICI8aW1nIi5jb25jYXQoYmVmb3JlLCAic3JjPVwiIikuY29uY2F0KGZ1bGxTcmMsICJcIiIpLmNvbmNhdChhZnRlciwgIj4iKTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIG1hdGNoOwogICAgICB9KQogICAgICAvLyDkv53nlZnmrrXokL3nu5PmnoQKICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnPHA+JykucmVwbGFjZSgvPFwvcD4vZ2ksICc8L3A+JykKICAgICAgLy8g5L+d55WZ5o2i6KGMCiAgICAgIC5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnPGJyPicpCiAgICAgIC8vIOa4heeQhuWkmuS9meeahOepuueZveauteiQvQogICAgICAucmVwbGFjZSgvPHA+XHMqPFwvcD4vZ2ksICcnKS5yZXBsYWNlKC8oPHA+W1xzXG5dKjxcL3A+KS9naSwgJycpOwogICAgICByZXR1cm4gcHJvY2Vzc2VkQ29udGVudC50cmltKCk7CiAgICB9IGNhdGNoIChlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCfinYwgcHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcg5Ye66ZSZOicsIGVycm9yKTsKICAgICAgcmV0dXJuIGNvbnRlbnQ7CiAgICB9CiAgfSksICJzdHJpcEh0bWxUYWdzS2VlcEltYWdlcyIsIGZ1bmN0aW9uIHN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnQpIHsKICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgcmV0dXJuICcnOwogICAgfQogICAgdHJ5IHsKICAgICAgLy8g5YWI5L+d5a2Y5omA5pyJ5Zu+54mH5qCH562+CiAgICAgIHZhciBpbWFnZXMgPSBbXTsKICAgICAgdmFyIGltYWdlSW5kZXggPSAwOwogICAgICB2YXIgY29udGVudFdpdGhQbGFjZWhvbGRlcnMgPSBjb250ZW50LnJlcGxhY2UoLzxpbWdbXj5dKj4vZ2ksIGZ1bmN0aW9uIChtYXRjaCkgewogICAgICAgIGltYWdlcy5wdXNoKG1hdGNoKTsKICAgICAgICByZXR1cm4gIlxuX19JTUFHRV9QTEFDRUhPTERFUl8iLmNvbmNhdChpbWFnZUluZGV4KyssICJfX1xuIik7CiAgICAgIH0pOwoKICAgICAgLy8g56e76Zmk5YW25LuWSFRNTOagh+etvu+8jOS9huS/neeVmeaNouihjAogICAgICB2YXIgdGV4dENvbnRlbnQgPSBjb250ZW50V2l0aFBsYWNlaG9sZGVycy5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnXG4nKSAvLyBicuagh+etvui9rOaNouS4uuaNouihjAogICAgICAucmVwbGFjZSgvPFwvcD4vZ2ksICdcbicpIC8vIHDnu5PmnZ/moIfnrb7ovazmjaLkuLrmjaLooYwKICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnXG4nKSAvLyBw5byA5aeL5qCH562+6L2s5o2i5Li65o2i6KGMCiAgICAgIC5yZXBsYWNlKC88W14+XSo+L2csICcnKSAvLyDnp7vpmaTlhbbku5ZIVE1M5qCH562+CiAgICAgIC5yZXBsYWNlKC9cblxzKlxuL2csICdcbicpOyAvLyDlkIjlubblpJrkuKrmjaLooYwKCiAgICAgIC8vIOaBouWkjeWbvueJh+agh+etvgogICAgICB2YXIgZmluYWxDb250ZW50ID0gdGV4dENvbnRlbnQ7CiAgICAgIGltYWdlcy5mb3JFYWNoKGZ1bmN0aW9uIChpbWcsIGluZGV4KSB7CiAgICAgICAgdmFyIHBsYWNlaG9sZGVyID0gIl9fSU1BR0VfUExBQ0VIT0xERVJfIi5jb25jYXQoaW5kZXgsICJfXyIpOwogICAgICAgIGlmIChmaW5hbENvbnRlbnQuaW5jbHVkZXMocGxhY2Vob2xkZXIpKSB7CiAgICAgICAgICBmaW5hbENvbnRlbnQgPSBmaW5hbENvbnRlbnQucmVwbGFjZShwbGFjZWhvbGRlciwgaW1nKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gZmluYWxDb250ZW50LnRyaW0oKTsKICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBzdHJpcEh0bWxUYWdzS2VlcEltYWdlcyDlh7rplJk6JywgZXJyb3IpOwogICAgICByZXR1cm4gY29udGVudDsKICAgIH0KICB9KSwgInBhcnNlT3B0aW9uc0Zyb21MaW5lcyIsIGZ1bmN0aW9uIHBhcnNlT3B0aW9uc0Zyb21MaW5lcyhsaW5lcywgc3RhcnRJbmRleCkgewogICAgdmFyIG9wdGlvbnMgPSBbXTsKICAgIGlmICghQXJyYXkuaXNBcnJheShsaW5lcykgfHwgc3RhcnRJbmRleCA8IDAgfHwgc3RhcnRJbmRleCA+PSBsaW5lcy5sZW5ndGgpIHsKICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g6Kej5p6Q6YCJ6aG55Y+C5pWw5peg5pWIJyk7CiAgICAgIHJldHVybiB7CiAgICAgICAgb3B0aW9uczogb3B0aW9ucwogICAgICB9OwogICAgfQogICAgdHJ5IHsKICAgICAgZm9yICh2YXIgaSA9IHN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIHZhciBsaW5lID0gbGluZXNbaV07CiAgICAgICAgaWYgKCFsaW5lIHx8IHR5cGVvZiBsaW5lICE9PSAnc3RyaW5nJykgewogICAgICAgICAgY29udGludWU7CiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrpgInpobnmoLzlvI/vvIhBOu+8ie+8jOWtl+avjeWPr+S7peS4ukHliLBa55qE5Lu75oSP5aSn5bCP5YaZ5a2X5q+N77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6IjrvvJrjgIEu77yOIuWFtuS4reS5i+S4gAogICAgICAgIHZhciBvcHRpb25NYXRjaCA9IGxpbmUubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKTsKICAgICAgICBpZiAob3B0aW9uTWF0Y2gpIHsKICAgICAgICAgIHZhciBvcHRpb25LZXkgPSBvcHRpb25NYXRjaFsxXS50b1VwcGVyQ2FzZSgpOwogICAgICAgICAgdmFyIG9wdGlvbkNvbnRlbnQgPSBvcHRpb25NYXRjaFsyXSA/IG9wdGlvbk1hdGNoWzJdLnRyaW0oKSA6ICcnOwogICAgICAgICAgaWYgKG9wdGlvbktleSAmJiBvcHRpb25Db250ZW50KSB7CiAgICAgICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgb3B0aW9uS2V5OiBvcHRpb25LZXksCiAgICAgICAgICAgICAgbGFiZWw6IG9wdGlvbktleSwKICAgICAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb25Db250ZW50LAogICAgICAgICAgICAgIGNvbnRlbnQ6IG9wdGlvbkNvbnRlbnQKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmlzQW5zd2VyTGluZShsaW5lKSB8fCB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNEaWZmaWN1bHR5TGluZShsaW5lKSkgewogICAgICAgICAgLy8g6YGH5Yiw562U5qGI44CB6Kej5p6Q5oiW6Zq+5bqm6KGM77yM5YGc5q2i6Kej5p6Q6YCJ6aG5CiAgICAgICAgICBicmVhazsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g6KeE6IyD77ya6YCJ6aG55LiO6YCJ6aG55LmL6Ze077yM5Y+v5Lul5o2i6KGM77yM5Lmf5Y+v5Lul5Zyo5ZCM5LiA6KGMCiAgICAgICAgICAvLyDlpoLmnpzpgInpobnlnKjlkIzkuIDooYzvvIzpgInpobnkuYvpl7Toh7PlsJHpnIDopoHmnInkuIDkuKrnqbrmoLwKICAgICAgICAgIHZhciBtdWx0aXBsZU9wdGlvbnNNYXRjaCA9IGxpbmUubWF0Y2goLyhbQS1aYS16XVsuOu+8mu+8juOAgV1ccypbXlxzXSsoPzpccytbQS1aYS16XVsuOu+8mu+8juOAgV1ccypbXlxzXSspKikvZyk7CiAgICAgICAgICBpZiAobXVsdGlwbGVPcHRpb25zTWF0Y2gpIHsKICAgICAgICAgICAgLy8g5aSE55CG5ZCM5LiA6KGM5aSa5Liq6YCJ6aG555qE5oOF5Ya1CiAgICAgICAgICAgIHZhciBzaW5nbGVPcHRpb25zID0gbGluZS5zcGxpdCgvXHMrKD89W0EtWmEtel1bLjrvvJrvvI7jgIFdKS8pOwogICAgICAgICAgICB2YXIgX2l0ZXJhdG9yID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShzaW5nbGVPcHRpb25zKSwKICAgICAgICAgICAgICBfc3RlcDsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgdmFyIHNpbmdsZU9wdGlvbiA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgICAgICAgaWYgKCFzaW5nbGVPcHRpb24pIGNvbnRpbnVlOwogICAgICAgICAgICAgICAgdmFyIG1hdGNoID0gc2luZ2xlT3B0aW9uLm1hdGNoKC9eKFtBLVphLXpdKVsuOu+8mu+8juOAgV1ccyooLiopLyk7CiAgICAgICAgICAgICAgICBpZiAobWF0Y2gpIHsKICAgICAgICAgICAgICAgICAgdmFyIF9vcHRpb25LZXkgPSBtYXRjaFsxXS50b1VwcGVyQ2FzZSgpOwogICAgICAgICAgICAgICAgICB2YXIgX29wdGlvbkNvbnRlbnQgPSBtYXRjaFsyXSA/IG1hdGNoWzJdLnRyaW0oKSA6ICcnOwogICAgICAgICAgICAgICAgICBpZiAoX29wdGlvbktleSAmJiBfb3B0aW9uQ29udGVudCkgewogICAgICAgICAgICAgICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25LZXk6IF9vcHRpb25LZXksCiAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogX29wdGlvbktleSwKICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IF9vcHRpb25Db250ZW50LAogICAgICAgICAgICAgICAgICAgICAgY29udGVudDogX29wdGlvbkNvbnRlbnQKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yLmUoZXJyKTsKICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9IGNhdGNoIChlcnJvcikgewogICAgICBjb25zb2xlLmVycm9yKCfinYwg6Kej5p6Q6YCJ6aG55pe25Ye66ZSZOicsIGVycm9yKTsKICAgIH0KICAgIHJldHVybiB7CiAgICAgIG9wdGlvbnM6IG9wdGlvbnMKICAgIH07CiAgfSksICJwYXJzZVF1ZXN0aW9uTWV0YUZyb21MaW5lcyIsIGZ1bmN0aW9uIHBhcnNlUXVlc3Rpb25NZXRhRnJvbUxpbmVzKGxpbmVzLCBxdWVzdGlvbikgewogICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICB2YXIgbGluZSA9IGxpbmVzW2ldOwoKICAgICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICB2YXIgYW5zd2VyTWF0Y2ggPSBsaW5lLm1hdGNoKC9e562U5qGIWy4677ya44CBXVxzKiguKykvKTsKICAgICAgaWYgKGFuc3dlck1hdGNoKSB7CiAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXJNYXRjaFsxXSwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKTsKICAgICAgICBjb250aW51ZTsKICAgICAgfQoKICAgICAgLy8g6KeE6IyD77ya6Kej5p6Q5qC85byP77yI6Kej5p6Q77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICB2YXIgZXhwbGFuYXRpb25NYXRjaCA9IGxpbmUubWF0Y2goL17op6PmnpBbLjrvvJrjgIFdXHMqKC4rKS8pOwogICAgICBpZiAoZXhwbGFuYXRpb25NYXRjaCkgewogICAgICAgIHF1ZXN0aW9uLmV4cGxhbmF0aW9uID0gZXhwbGFuYXRpb25NYXRjaFsxXS50cmltKCk7CiAgICAgICAgY29udGludWU7CiAgICAgIH0KCiAgICAgIC8vIOinhOiMg++8mumavuW6puagvOW8j++8iOmavuW6pu+8mu+8ie+8jOWPquaUr+aMgeeugOWNleOAgeS4reetieOAgeWbsOmavuS4ieS4que6p+WIqwogICAgICB2YXIgZGlmZmljdWx0eU1hdGNoID0gbGluZS5tYXRjaCgvXumavuW6plsuOu+8muOAgV1ccyoo566A5Y2VfOS4reetiXzlm7Dpmr585LitKS8pOwogICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgdmFyIGRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5TWF0Y2hbMV07CiAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgIGlmIChkaWZmaWN1bHR5ID09PSAn5LitJykgewogICAgICAgICAgZGlmZmljdWx0eSA9ICfkuK3nrYknOwogICAgICAgIH0KICAgICAgICAvLyDlj6rmjqXlj5fmoIflh4bnmoTkuInkuKrpmr7luqbnuqfliKsKICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICBxdWVzdGlvbi5kaWZmaWN1bHR5ID0gZGlmZmljdWx0eTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g5LiN5pSv5oyB55qE6Zq+5bqm57qn5YirOicsIGRpZmZpY3VsdHksICfvvIzlt7Llv73nlaUnKTsKICAgICAgICB9CiAgICAgICAgY29udGludWU7CiAgICAgIH0KICAgIH0KCiAgICAvLyDop4TojIPvvJrnrZTmoYjmlK/mjIHnm7TmjqXlnKjpopjlubLkuK3moIfms6jvvIzkvJjlhYjku6XmmL7lvI/moIfms6jnmoTnrZTmoYjkuLrlh4YKICAgIC8vIOWmguaenOayoeacieaJvuWIsOaYvuW8j+etlOahiO+8jOWwneivleS7jumimOebruWGheWuueS4reaPkOWPlgogICAgaWYgKCFxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSB7CiAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLmV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKTsKICAgIH0KICB9KSwgImV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50IiwgZnVuY3Rpb24gZXh0cmFjdEFuc3dlckZyb21RdWVzdGlvbkNvbnRlbnQocXVlc3Rpb25Db250ZW50LCBxdWVzdGlvblR5cGUpIHsKICAgIGlmICghcXVlc3Rpb25Db250ZW50IHx8IHR5cGVvZiBxdWVzdGlvbkNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgIHJldHVybiAnJzsKICAgIH0KICAgIHRyeSB7CiAgICAgIC8vIOinhOiMg++8mumimOW5suS4reagvOW8j++8iOOAkEHjgJHvvInvvIzmi6zlj7flj6/ku6Xmm7/mjaLkuLrkuK3oi7HmlofnmoTlsI/mi6zlj7fmiJbogIXkuK3mi6zlj7cKICAgICAgdmFyIHBhdHRlcm5zID0gWy/jgJAoW17jgJFdKynjgJEvZywKICAgICAgLy8g5Lit5paH5pa55ous5Y+3CiAgICAgIC9cWyhbXlxdXSspXF0vZywKICAgICAgLy8g6Iux5paH5pa55ous5Y+3CiAgICAgIC/vvIgoW17vvIldKynvvIkvZywKICAgICAgLy8g5Lit5paH5ZyG5ous5Y+3CiAgICAgIC9cKChbXildKylcKS9nIC8vIOiLseaWh+WchuaLrOWPtwogICAgICBdOwogICAgICBmb3IgKHZhciBfaTMgPSAwLCBfcGF0dGVybnMgPSBwYXR0ZXJuczsgX2kzIDwgX3BhdHRlcm5zLmxlbmd0aDsgX2kzKyspIHsKICAgICAgICB2YXIgcGF0dGVybiA9IF9wYXR0ZXJuc1tfaTNdOwogICAgICAgIHZhciBtYXRjaGVzID0gcXVlc3Rpb25Db250ZW50Lm1hdGNoKHBhdHRlcm4pOwogICAgICAgIGlmIChtYXRjaGVzICYmIG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgLy8g5o+Q5Y+W5pyA5ZCO5LiA5Liq5Yy56YWN6aG55L2c5Li6562U5qGI77yI6YCa5bi4562U5qGI5Zyo6aKY55uu5pyr5bC+77yJCiAgICAgICAgICB2YXIgbGFzdE1hdGNoID0gbWF0Y2hlc1ttYXRjaGVzLmxlbmd0aCAtIDFdOwogICAgICAgICAgdmFyIGFuc3dlciA9IGxhc3RNYXRjaC5yZXBsYWNlKC9b44CQ44CRXFtcXe+8iO+8iSgpXS9nLCAnJykudHJpbSgpOwogICAgICAgICAgaWYgKGFuc3dlcikgewogICAgICAgICAgICByZXR1cm4gdGhpcy5wYXJzZUFuc3dlclZhbHVlKGFuc3dlciwgcXVlc3Rpb25UeXBlKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDku47popjlubLmj5Dlj5bnrZTmoYjml7blh7rplJk6JywgZXJyb3IpOwogICAgfQogICAgcmV0dXJuICcnOwogIH0pLCAicGFyc2VBbnN3ZXJWYWx1ZSIsIGZ1bmN0aW9uIHBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyVGV4dCwgcXVlc3Rpb25UeXBlKSB7CiAgICBpZiAoIWFuc3dlclRleHQgfHwgdHlwZW9mIGFuc3dlclRleHQgIT09ICdzdHJpbmcnKSB7CiAgICAgIHJldHVybiAnJzsKICAgIH0KICAgIHRyeSB7CiAgICAgIHZhciB0cmltbWVkQW5zd2VyID0gYW5zd2VyVGV4dC50cmltKCk7CiAgICAgIGlmICghdHJpbW1lZEFuc3dlcikgewogICAgICAgIHJldHVybiAnJzsKICAgICAgfQogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnKSB7CiAgICAgICAgLy8g5Yik5pat6aKY562U5qGI5aSE55CGIC0g5L+d5oyB5Y6f5aeL5qC85byP77yM5LiN6L2s5o2i5Li6dHJ1ZS9mYWxzZQogICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOmAieaLqemimOetlOahiOWkhOeQhgogICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyLnRvVXBwZXJDYXNlKCk7CiAgICAgIH0KICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDop6PmnpDnrZTmoYjlgLzml7blh7rplJk6JywgZXJyb3IpOwogICAgICByZXR1cm4gYW5zd2VyVGV4dCB8fCAnJzsKICAgIH0KICB9KSwgInNwbGl0QnlRdWVzdGlvblR5cGUiLCBmdW5jdGlvbiBzcGxpdEJ5UXVlc3Rpb25UeXBlKGNvbnRlbnQpIHsKICAgIHZhciBzZWN0aW9ucyA9IFtdOwogICAgdmFyIHR5cGVSZWdleCA9IC9cWyjljZXpgInpoph85aSa6YCJ6aKYfOWIpOaWremimClcXS9nOwogICAgdmFyIGxhc3RJbmRleCA9IDA7CiAgICB2YXIgbWF0Y2g7CiAgICB2YXIgY3VycmVudFR5cGUgPSBudWxsOwogICAgd2hpbGUgKChtYXRjaCA9IHR5cGVSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkgewogICAgICBpZiAoY3VycmVudFR5cGUpIHsKICAgICAgICAvLyDkv53lrZjkuIrkuIDkuKrljLrln58KICAgICAgICBzZWN0aW9ucy5wdXNoKHsKICAgICAgICAgIHR5cGU6IGN1cnJlbnRUeXBlLAogICAgICAgICAgY29udGVudDogY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4LCBtYXRjaC5pbmRleCkudHJpbSgpCiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgY3VycmVudFR5cGUgPSBtYXRjaFsxXTsKICAgICAgbGFzdEluZGV4ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGg7CiAgICB9CgogICAgLy8g5L+d5a2Y5pyA5ZCO5LiA5Liq5Yy65Z+fCiAgICBpZiAoY3VycmVudFR5cGUpIHsKICAgICAgc2VjdGlvbnMucHVzaCh7CiAgICAgICAgdHlwZTogY3VycmVudFR5cGUsCiAgICAgICAgY29udGVudDogY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4KS50cmltKCkKICAgICAgfSk7CiAgICB9CiAgICByZXR1cm4gc2VjdGlvbnM7CiAgfSksICJwYXJzZVNlY3Rpb25RdWVzdGlvbnMiLCBmdW5jdGlvbiBwYXJzZVNlY3Rpb25RdWVzdGlvbnMoc2VjdGlvbikgewogICAgdmFyIF90aGlzMTYgPSB0aGlzOwogICAgdmFyIHF1ZXN0aW9ucyA9IFtdOwogICAgdmFyIHF1ZXN0aW9uVHlwZSA9IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZShzZWN0aW9uLnR5cGUpOwoKICAgIC8vIOaMiemimOWPt+WIhuWJsumimOebrgogICAgdmFyIHF1ZXN0aW9uQmxvY2tzID0gdGhpcy5zcGxpdEJ5UXVlc3Rpb25OdW1iZXIoc2VjdGlvbi5jb250ZW50KTsKICAgIHF1ZXN0aW9uQmxvY2tzLmZvckVhY2goZnVuY3Rpb24gKGJsb2NrLCBpbmRleCkgewogICAgICB0cnkgewogICAgICAgIHZhciBxdWVzdGlvbiA9IF90aGlzMTYucGFyc2VRdWVzdGlvbkJsb2NrKGJsb2NrLCBxdWVzdGlvblR5cGUsIGluZGV4ICsgMSk7CiAgICAgICAgaWYgKHF1ZXN0aW9uKSB7CiAgICAgICAgICBxdWVzdGlvbnMucHVzaChxdWVzdGlvbik7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRocm93IG5ldyBFcnJvcigiXHU3QjJDIi5jb25jYXQoaW5kZXggKyAxLCAiXHU5ODk4XHU4OUUzXHU2NzkwXHU1OTMxXHU4RDI1OiAiKS5jb25jYXQoZXJyb3IubWVzc2FnZSkpOwogICAgICB9CiAgICB9KTsKICAgIHJldHVybiBxdWVzdGlvbnM7CiAgfSksICJzcGxpdEJ5UXVlc3Rpb25OdW1iZXIiLCBmdW5jdGlvbiBzcGxpdEJ5UXVlc3Rpb25OdW1iZXIoY29udGVudCkgewogICAgdmFyIGJsb2NrcyA9IFtdOwogICAgdmFyIG51bWJlclJlZ2V4ID0gL15ccyooXGQrKVsuOu+8mu+8jl1ccyovZ207CiAgICB2YXIgbGFzdEluZGV4ID0gMDsKICAgIHZhciBtYXRjaDsKICAgIHdoaWxlICgobWF0Y2ggPSBudW1iZXJSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkgewogICAgICBpZiAobGFzdEluZGV4ID4gMCkgewogICAgICAgIC8vIOS/neWtmOS4iuS4gOmimAogICAgICAgIGJsb2Nrcy5wdXNoKGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCwgbWF0Y2guaW5kZXgpLnRyaW0oKSk7CiAgICAgIH0KICAgICAgbGFzdEluZGV4ID0gbWF0Y2guaW5kZXg7CiAgICB9CgogICAgLy8g5L+d5a2Y5pyA5ZCO5LiA6aKYCiAgICBpZiAobGFzdEluZGV4IDwgY29udGVudC5sZW5ndGgpIHsKICAgICAgYmxvY2tzLnB1c2goY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4KS50cmltKCkpOwogICAgfQogICAgcmV0dXJuIGJsb2Nrcy5maWx0ZXIoZnVuY3Rpb24gKGJsb2NrKSB7CiAgICAgIHJldHVybiBibG9jay5sZW5ndGggPiAwOwogICAgfSk7CiAgfSksICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9tZXRob2RzLCAicGFyc2VRdWVzdGlvbkJsb2NrIiwgZnVuY3Rpb24gcGFyc2VRdWVzdGlvbkJsb2NrKGJsb2NrLCBxdWVzdGlvblR5cGUpIHsKICAgIHZhciBsaW5lcyA9IGJsb2NrLnNwbGl0KCdcbicpLm1hcChmdW5jdGlvbiAobGluZSkgewogICAgICByZXR1cm4gbGluZS50cmltKCk7CiAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKGxpbmUpIHsKICAgICAgcmV0dXJuIGxpbmUubGVuZ3RoID4gMDsKICAgIH0pOwogICAgaWYgKGxpbmVzLmxlbmd0aCA9PT0gMCkgewogICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOebruWGheWuueS4uuepuicpOwogICAgfQoKICAgIC8vIOaPkOWPlumimOW5su+8iOenu+mZpOmimOWPt++8iQogICAgdmFyIGZpcnN0TGluZSA9IGxpbmVzWzBdOwogICAgdmFyIHF1ZXN0aW9uQ29udGVudCA9ICcnOwogICAgdmFyIGN1cnJlbnRMaW5lSW5kZXggPSAwOwoKICAgIC8vIOWmguaenOesrOS4gOihjOWMheWQq+mimOWPt++8jOenu+mZpOmimOWPt+mDqOWIhgogICAgdmFyIG51bWJlck1hdGNoID0gZmlyc3RMaW5lLm1hdGNoKC9eXHMqKFxkKylbLjrvvJrvvI7jgIFdXHMqKC4qKS8pOwogICAgaWYgKG51bWJlck1hdGNoKSB7CiAgICAgIHF1ZXN0aW9uQ29udGVudCA9IG51bWJlck1hdGNoWzJdLnRyaW0oKTsgLy8g56e76Zmk6aKY5Y+377yM5Y+q5L+d55WZ6aKY5bmyCiAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSAxOwogICAgfSBlbHNlIHsKICAgICAgLy8g5aaC5p6c56ys5LiA6KGM5LiN5YyF5ZCr6aKY5Y+377yM55u05o6l5L2c5Li66aKY5bmy77yM5L2G5LuN6ZyA5riF55CG5Y+v6IO955qE6aKY5Y+3CiAgICAgIHF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIoZmlyc3RMaW5lKS50cmltKCk7CiAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSAxOwogICAgfQoKICAgIC8vIOe7p+e7reivu+WPlumimOW5suWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhue+8iQogICAgd2hpbGUgKGN1cnJlbnRMaW5lSW5kZXggPCBsaW5lcy5sZW5ndGgpIHsKICAgICAgdmFyIGxpbmUgPSBsaW5lc1tjdXJyZW50TGluZUluZGV4XTsKICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKGxpbmUpKSB7CiAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgICAgcXVlc3Rpb25Db250ZW50ICs9ICdcbicgKyBsaW5lOwogICAgICBjdXJyZW50TGluZUluZGV4Kys7CiAgICB9CiAgICB2YXIgcXVlc3Rpb24gPSB7CiAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICBxdWVzdGlvbkNvbnRlbnQ6IHF1ZXN0aW9uQ29udGVudC50cmltKCksCiAgICAgIGRpZmZpY3VsdHk6ICcnLAogICAgICAvLyDkuI3orr7nva7pu5jorqTlgLwKICAgICAgZXhwbGFuYXRpb246ICcnLAogICAgICBvcHRpb25zOiBbXSwKICAgICAgY29ycmVjdEFuc3dlcjogJycKICAgIH07CgogICAgLy8g6Kej5p6Q6YCJ6aG577yI5a+55LqO6YCJ5oup6aKY77yJCiAgICBpZiAocXVlc3Rpb25UeXBlICE9PSAnanVkZ21lbnQnKSB7CiAgICAgIHZhciBvcHRpb25SZXN1bHQgPSB0aGlzLnBhcnNlT3B0aW9ucyhsaW5lcywgY3VycmVudExpbmVJbmRleCk7CiAgICAgIHF1ZXN0aW9uLm9wdGlvbnMgPSBvcHRpb25SZXN1bHQub3B0aW9uczsKICAgICAgY3VycmVudExpbmVJbmRleCA9IG9wdGlvblJlc3VsdC5uZXh0SW5kZXg7CiAgICB9CgogICAgLy8g6Kej5p6Q562U5qGI44CB6Kej5p6Q44CB6Zq+5bqmCiAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhKGxpbmVzLCBjdXJyZW50TGluZUluZGV4LCBxdWVzdGlvbik7CgogICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655a6M5YWo5rKh5pyJ6aKY5Y+3CiAgICBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCk7CiAgICByZXR1cm4gcXVlc3Rpb247CiAgfSksICJpc09wdGlvbkxpbmUiLCBmdW5jdGlvbiBpc09wdGlvbkxpbmUobGluZSkgewogICAgcmV0dXJuIC9eW0EtWmEtel1bLjrvvJrvvI5dXHMqLy50ZXN0KGxpbmUpOwogIH0pLCAicGFyc2VPcHRpb25zIiwgZnVuY3Rpb24gcGFyc2VPcHRpb25zKGxpbmVzLCBzdGFydEluZGV4KSB7CiAgICB2YXIgb3B0aW9ucyA9IFtdOwogICAgdmFyIGN1cnJlbnRJbmRleCA9IHN0YXJ0SW5kZXg7CiAgICB3aGlsZSAoY3VycmVudEluZGV4IDwgbGluZXMubGVuZ3RoKSB7CiAgICAgIHZhciBsaW5lID0gbGluZXNbY3VycmVudEluZGV4XTsKICAgICAgdmFyIG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI5dXHMqKC4qKS8pOwogICAgICBpZiAoIW9wdGlvbk1hdGNoKSB7CiAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICBvcHRpb25LZXk6IG9wdGlvbk1hdGNoWzFdLnRvVXBwZXJDYXNlKCksCiAgICAgICAgb3B0aW9uQ29udGVudDogb3B0aW9uTWF0Y2hbMl0udHJpbSgpCiAgICAgIH0pOwogICAgICBjdXJyZW50SW5kZXgrKzsKICAgIH0KICAgIHJldHVybiB7CiAgICAgIG9wdGlvbnM6IG9wdGlvbnMsCiAgICAgIG5leHRJbmRleDogY3VycmVudEluZGV4CiAgICB9OwogIH0pLCAicGFyc2VRdWVzdGlvbk1ldGEiLCBmdW5jdGlvbiBwYXJzZVF1ZXN0aW9uTWV0YShsaW5lcywgc3RhcnRJbmRleCwgcXVlc3Rpb24pIHsKICAgIGZvciAodmFyIGkgPSBzdGFydEluZGV4OyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgdmFyIGxpbmUgPSBsaW5lc1tpXTsKCiAgICAgIC8vIOino+aekOetlOahiAogICAgICB2YXIgYW5zd2VyTWF0Y2ggPSBsaW5lLm1hdGNoKC9e562U5qGIW++8mjpdXHMqKC4rKS8pOwogICAgICBpZiAoYW5zd2VyTWF0Y2gpIHsKICAgICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5wYXJzZUFuc3dlcihhbnN3ZXJNYXRjaFsxXSwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKTsKICAgICAgICBjb250aW51ZTsKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q6Kej5p6QCiAgICAgIHZhciBleHBsYW5hdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXuino+aekFvvvJo6XVxzKiguKykvKTsKICAgICAgaWYgKGV4cGxhbmF0aW9uTWF0Y2gpIHsKICAgICAgICBxdWVzdGlvbi5leHBsYW5hdGlvbiA9IGV4cGxhbmF0aW9uTWF0Y2hbMV0udHJpbSgpOwogICAgICAgIGNvbnRpbnVlOwogICAgICB9CgogICAgICAvLyDop6PmnpDpmr7luqYgLSDlj6rmlK/mjIHnroDljZXjgIHkuK3nrYnjgIHlm7Dpmr7kuInkuKrnuqfliKsKICAgICAgdmFyIGRpZmZpY3VsdHlNYXRjaCA9IGxpbmUubWF0Y2goL17pmr7luqZb77yaOl1ccyoo566A5Y2VfOS4reetiXzlm7Dpmr585LitKS8pOwogICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgdmFyIGRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5TWF0Y2hbMV07CiAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgIGlmIChkaWZmaWN1bHR5ID09PSAn5LitJykgewogICAgICAgICAgZGlmZmljdWx0eSA9ICfkuK3nrYknOwogICAgICAgIH0KICAgICAgICAvLyDlj6rmjqXlj5fmoIflh4bnmoTkuInkuKrpmr7luqbnuqfliKsKICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICBxdWVzdGlvbi5kaWZmaWN1bHR5ID0gZGlmZmljdWx0eTsKICAgICAgICB9CiAgICAgICAgY29udGludWU7CiAgICAgIH0KICAgIH0KCiAgICAvLyDlpoLmnpzmsqHmnInmmL7lvI/nrZTmoYjvvIzlsJ3or5Xku47popjlubLkuK3mj5Dlj5YKICAgIGlmICghcXVlc3Rpb24uY29ycmVjdEFuc3dlcikgewogICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5leHRyYWN0QW5zd2VyRnJvbUNvbnRlbnQocXVlc3Rpb24ucXVlc3Rpb25Db250ZW50LCBxdWVzdGlvbi5xdWVzdGlvblR5cGUpOwogICAgfQogIH0pLCAiZXh0cmFjdEFuc3dlckZyb21Db250ZW50IiwgZnVuY3Rpb24gZXh0cmFjdEFuc3dlckZyb21Db250ZW50KGNvbnRlbnQsIHF1ZXN0aW9uVHlwZSkgewogICAgLy8g5pSv5oyB55qE5ous5Y+357G75Z6LCiAgICB2YXIgYnJhY2tldFBhdHRlcm5zID0gWy/jgJAoW17jgJFdKynjgJEvZywgL1xbKFteXF1dKylcXS9nLCAv77yIKFte77yJXSsp77yJL2csIC9cKChbXildKylcKS9nXTsKICAgIGZvciAodmFyIF9pNCA9IDAsIF9icmFja2V0UGF0dGVybnMgPSBicmFja2V0UGF0dGVybnM7IF9pNCA8IF9icmFja2V0UGF0dGVybnMubGVuZ3RoOyBfaTQrKykgewogICAgICB2YXIgcGF0dGVybiA9IF9icmFja2V0UGF0dGVybnNbX2k0XTsKICAgICAgdmFyIG1hdGNoZXMgPSAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShjb250ZW50Lm1hdGNoQWxsKHBhdHRlcm4pKTsKICAgICAgaWYgKG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgIHZhciBhbnN3ZXIgPSBtYXRjaGVzW21hdGNoZXMubGVuZ3RoIC0gMV1bMV07IC8vIOWPluacgOWQjuS4gOS4quWMuemFjQogICAgICAgIHJldHVybiB0aGlzLnBhcnNlQW5zd2VyKGFuc3dlciwgcXVlc3Rpb25UeXBlKTsKICAgICAgfQogICAgfQogICAgcmV0dXJuICcnOwogIH0pLCAiY29udmVydFF1ZXN0aW9uVHlwZSIsIGZ1bmN0aW9uIGNvbnZlcnRRdWVzdGlvblR5cGUodHlwZVRleHQpIHsKICAgIHZhciB0eXBlTWFwID0gewogICAgICAn5Y2V6YCJ6aKYJzogJ3NpbmdsZScsCiAgICAgICflpJrpgInpopgnOiAnbXVsdGlwbGUnLAogICAgICAn5Yik5pat6aKYJzogJ2p1ZGdtZW50JwogICAgfTsKICAgIHJldHVybiB0eXBlTWFwW3R5cGVUZXh0XSB8fCAnc2luZ2xlJzsKICB9KSwgImdldFF1ZXN0aW9uVHlwZU5hbWUiLCBmdW5jdGlvbiBnZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpIHsKICAgIHZhciB0eXBlTWFwID0gewogICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAnanVkZ21lbnQnOiAn5Yik5pat6aKYJwogICAgfTsKICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6UnOwogIH0pLCAiZ2V0UXVlc3Rpb25UeXBlQ29sb3IiLCBmdW5jdGlvbiBnZXRRdWVzdGlvblR5cGVDb2xvcih0eXBlKSB7CiAgICB2YXIgY29sb3JNYXAgPSB7CiAgICAgICdzaW5nbGUnOiAncHJpbWFyeScsCiAgICAgICdtdWx0aXBsZSc6ICdzdWNjZXNzJywKICAgICAgJ2p1ZGdtZW50JzogJ3dhcm5pbmcnCiAgICB9OwogICAgcmV0dXJuIGNvbG9yTWFwW3R5cGVdIHx8ICdpbmZvJzsKICB9KSwgImdldEZvcm1hdHRlZFF1ZXN0aW9uQ29udGVudCIsIGZ1bmN0aW9uIGdldEZvcm1hdHRlZFF1ZXN0aW9uQ29udGVudChxdWVzdGlvbikgewogICAgaWYgKCFxdWVzdGlvbiB8fCAhcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KSB7CiAgICAgIHJldHVybiAnJzsKICAgIH0KICAgIHZhciBjb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50OwoKICAgIC8vIOWmguaenOaciUhUTUzlhoXlrrnkuJTljIXlkKvlr4zmlofmnKzmoIfnrb7vvIzkvJjlhYjkvb/nlKhIVE1M5YaF5a65CiAgICBpZiAodGhpcy5kb2N1bWVudEh0bWxDb250ZW50ICYmIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudC5pbmNsdWRlcygnPCcpKSB7CiAgICAgIC8vIOS7jkhUTUzlhoXlrrnkuK3mj5Dlj5blr7nlupTnmoTpopjnm67lhoXlrrkKICAgICAgdmFyIGh0bWxDb250ZW50ID0gdGhpcy5leHRyYWN0UXVlc3Rpb25Gcm9tSHRtbChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCk7CiAgICAgIGlmIChodG1sQ29udGVudCkgewogICAgICAgIGNvbnRlbnQgPSBodG1sQ29udGVudDsKICAgICAgfQogICAgfQoKICAgIC8vIOa4heeQhumimOWPt++8muehruS/nemimOebruWGheWuueS4jeS7peaVsOWtlyvnrKblj7flvIDlpLQKICAgIGNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpOwogICAgcmV0dXJuIHRoaXMucHJvY2Vzc0ltYWdlUGF0aHMoY29udGVudCk7CiAgfSksICJyZW1vdmVRdWVzdGlvbk51bWJlciIsIGZ1bmN0aW9uIHJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpIHsKICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgcmV0dXJuIGNvbnRlbnQ7CiAgICB9CgogICAgLy8g5aSE55CGSFRNTOWGheWuuQogICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAvLyDlr7nkuo5IVE1M5YaF5a6577yM6ZyA6KaB5riF55CG5qCH562+5YaF55qE6aKY5Y+3CiAgICAgIHJldHVybiBjb250ZW50LnJlcGxhY2UoLzxwW14+XSo+KFxzKlxkK1suOu+8mu+8juOAgV1ccyopKC4qPyk8XC9wPi9naSwgJzxwPiQyPC9wPicpLnJlcGxhY2UoL14oXHMqXGQrWy4677ya77yO44CBXVxzKikvLCAnJykgLy8g5riF55CG5byA5aS055qE6aKY5Y+3CiAgICAgIC5yZXBsYWNlKC8+XHMqXGQrWy4677ya77yO44CBXVxzKi9nLCAnPicpOyAvLyDmuIXnkIbmoIfnrb7lkI7nmoTpopjlj7cKICAgIH0gZWxzZSB7CiAgICAgIC8vIOWvueS6jue6r+aWh+acrOWGheWuue+8jOebtOaOpea4heeQhuW8gOWktOeahOmimOWPtwogICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCk7CiAgICB9CiAgfSksICgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKCgwLCBfZGVmaW5lUHJvcGVydHkyLmRlZmF1bHQpKF9tZXRob2RzLCAiZXh0cmFjdFF1ZXN0aW9uRnJvbUh0bWwiLCBmdW5jdGlvbiBleHRyYWN0UXVlc3Rpb25Gcm9tSHRtbChwbGFpbkNvbnRlbnQsIGh0bWxDb250ZW50KSB7CiAgICBpZiAoIXBsYWluQ29udGVudCB8fCAhaHRtbENvbnRlbnQpIHsKICAgICAgcmV0dXJuIHBsYWluQ29udGVudDsKICAgIH0KICAgIHRyeSB7CiAgICAgIC8vIOeugOWNleeahOWMuemFjeetlueVpe+8muafpeaJvuWMheWQq+mimOebruWGheWuueeahEhUTUzmrrXokL0KICAgICAgdmFyIHBsYWluVGV4dCA9IHBsYWluQ29udGVudC5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCk7CgogICAgICAvLyDlnKhIVE1M5YaF5a655Lit5p+l5om+5YyF5ZCr6L+Z5Liq5paH5pys55qE5q616JC9CiAgICAgIHZhciBwYXJhZ3JhcGhzID0gaHRtbENvbnRlbnQubWF0Y2goLzxwW14+XSo+Lio/PFwvcD4vZ2kpIHx8IFtdOwogICAgICB2YXIgX2l0ZXJhdG9yMiA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkocGFyYWdyYXBocyksCiAgICAgICAgX3N0ZXAyOwogICAgICB0cnkgewogICAgICAgIGZvciAoX2l0ZXJhdG9yMi5zKCk7ICEoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uKCkpLmRvbmU7KSB7CiAgICAgICAgICB2YXIgcGFyYWdyYXBoID0gX3N0ZXAyLnZhbHVlOwogICAgICAgICAgdmFyIHBhcmFncmFwaFRleHQgPSBwYXJhZ3JhcGgucmVwbGFjZSgvPFtePl0qPi9nLCAnJykudHJpbSgpOwogICAgICAgICAgLy8g5riF55CG5q616JC95paH5pys5Lit55qE6aKY5Y+35YaN6L+b6KGM5Yy56YWNCiAgICAgICAgICB2YXIgY2xlYW5QYXJhZ3JhcGhUZXh0ID0gcGFyYWdyYXBoVGV4dC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCk7CiAgICAgICAgICBpZiAoY2xlYW5QYXJhZ3JhcGhUZXh0LmluY2x1ZGVzKHBsYWluVGV4dC5zdWJzdHJpbmcoMCwgMjApKSkgewogICAgICAgICAgICAvLyDmib7liLDljLnphY3nmoTmrrXokL3vvIzov5Tlm55IVE1M5qC85byP77yI5L2G6KaB5riF55CG6aKY5Y+377yJCiAgICAgICAgICAgIHJldHVybiB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHBhcmFncmFwaCk7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDljLnphY3nmoTmrrXokL3vvIzov5Tlm57ljp/lp4vlhoXlrrkKICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yMi5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yMi5mKCk7CiAgICAgIH0KICAgICAgcmV0dXJuIHBsYWluQ29udGVudDsKICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aPkOWPlkhUTUzpopjnm67lhoXlrrnlpLHotKU6JywgZXJyb3IpOwogICAgICByZXR1cm4gcGxhaW5Db250ZW50OwogICAgfQogIH0pLCAiaGFuZGxlU2VhcmNoIiwgZnVuY3Rpb24gaGFuZGxlU2VhcmNoKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCk7CiAgfSksICJyZXNldFNlYXJjaCIsIGZ1bmN0aW9uIHJlc2V0U2VhcmNoKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVzdGlvblR5cGUgPSBudWxsOwogICAgdGhpcy5xdWVyeVBhcmFtcy5kaWZmaWN1bHR5ID0gbnVsbDsKICAgIHRoaXMucXVlcnlQYXJhbXMucXVlc3Rpb25Db250ZW50ID0gbnVsbDsKICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpOwogIH0pKQp9Ow=="}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "clearImportContent", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleBatchImportClick", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "_this6", "question", "handleEditQuestion", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this7", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "documentHtmlContent", "allExpanded", "isUploading", "isParsing", "importOptions", "showDocumentImportDialog", "_this8", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this9", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this0", "code", "setTimeout", "questions", "collapsed", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msg", "handleUploadError", "collapseAll", "_this1", "$set", "toggleQuestion", "toggleAllQuestions", "_this10", "confirmImport", "_this11", "importQuestions", "_this12", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "batchImportQuestions", "v", "Error", "a", "_this13", "window", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "_this14", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "console", "log", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this15", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "warn", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this16", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "getQuestionTypeColor", "colorMap", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2.题目数量过多、题目文件过大等情况建议分批导入<br>\n          3.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n\n            } catch (fallbackError) {\n\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            // 编辑器初始化完成，确保内容为空\n            this.richEditor.setData('')\n          })\n        })\n\n      } catch (error) {\n\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n\n      } catch (error) {\n\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n\n        if (lines.length === 0) {\n\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqgBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAAA,IAAAK,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,6BAEA,SAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,yBACA,wBACA,oBACA,oBAEA,+BAEA,uCACA,8BACA,yBAEA,4BAEA,qBACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,mBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,4BACA;EAEA;EAEAC,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAIA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAR,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAQ,KAAA;QACA,IAAAR,MAAA;UACA;UACA,KAAAS,kBAAA;UACA,KAAAC,SAAA;YACAF,KAAA,CAAAG,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAR,SAAA;IACA;EACA;EAEAS,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAb,qBAAA,QAAAc,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAjD,MAAA,OAAAA;IACA;IACA,KAAAkD,aAAA;MACA3B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAyB,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IAGA;IACA,SAAAV,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAW,OAAA,GAAA7D,QAAA;IACA;IACAsD,QAAA,WAAAA,SAAA;MACA,IAAAQ,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAAxD,MAAA,GAAAsD,kBAAA,CAAAtD,MAAA;QAAAC,QAAA,GAAAqD,kBAAA,CAAArD,QAAA;MACA,KAAAD,MAAA;QACA,KAAAyD,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAA3D,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAA4D,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAA1D,WAAA;MACA,IAAA2D,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAzD,YAAA,GAAA8D,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAA7D,KAAA,GAAAkE,QAAA,CAAAlE,KAAA;MACA,GAAAoE,KAAA,WAAAb,KAAA;QAEAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAxD,OAAA,MAAAgD,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAA7D,YAAA;QACA,IAAA+D,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAA7D,YAAA,GAAA+D,OAAA,CAAAF,eAAA,CAAA7D,YAAA,KAAA6D,eAAA,CAAA7D,YAAA;MACA;;MAEA;MACA,IAAA6D,eAAA,CAAA5D,UAAA;QACA,IAAA+D,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAA5D,UAAA,GAAA+D,aAAA,CAAAH,eAAA,CAAA5D,UAAA,KAAA4D,eAAA,CAAA5D,UAAA;MACA;;MAEA;MACAgE,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,+BAAA,OAAAlF,MAAA,EAAAoE,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAA/E,UAAA,GAAAmE,QAAA,CAAAvE,IAAA;MACA,GAAAyE,KAAA,WAAAb,KAAA;QAEA;QACAuB,MAAA,CAAA/E,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACA6E,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IAEA;IACAC,sBAAA,WAAAA,uBAAA;MACA,KAAAhD,mBAAA;IACA;IACA;IACAiD,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAA7E,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAA8E,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAA9E,iBAAA,CAAA+E,MAAA;QACA,KAAArC,QAAA,CAAAsC,OAAA;QACA;MACA;MACA,KAAAtC,QAAA,CAAAuC,IAAA,6BAAAC,MAAA,MAAAlF,iBAAA,CAAA+E,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAApF,iBAAA,QAAAR,YAAA,CAAA6F,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAA7C,QAAA,CAAA8C,OAAA,uBAAAN,MAAA,MAAAlF,iBAAA,CAAA+E,MAAA;MACA;QACA;QACA,KAAA/E,iBAAA;QACA,KAAA0C,QAAA,CAAA8C,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAA1F,iBAAA,CAAA+E,MAAA;QACA,KAAArC,QAAA,CAAAsC,OAAA;QACA;MACA;MAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAlF,iBAAA,CAAA+E,MAAA;QACAa,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAnB,IAAA;QACA;QACA;QACA,IAAAyC,cAAA,GAAAJ,MAAA,CAAA1F,iBAAA,CAAAqF,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAzC,IAAA;UACAqC,MAAA,CAAAhD,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAQ,MAAA,CAAA1F,iBAAA,CAAA+E,MAAA;UACAW,MAAA,CAAA1F,iBAAA;UACA0F,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAA7C,eAAA;UACA6C,MAAA,CAAA5C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UAEA+C,MAAA,CAAAhD,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAA1C,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAAAgH,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAAnG,iBAAA,CAAA+E,MAAA;MACA,KAAArC,QAAA,CAAAsC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAlF,iBAAA,CAAA+E,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACA;MACA,IAAAyC,cAAA,GAAAK,MAAA,CAAAnG,iBAAA,CAAAqF,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;MAAA,CACA;MAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAzC,IAAA;QACA8C,MAAA,CAAAzD,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAiB,MAAA,CAAAnG,iBAAA,CAAA+E,MAAA;QACAoB,MAAA,CAAAnG,iBAAA;QACAmG,MAAA,CAAAf,aAAA;QACAe,MAAA,CAAAtD,eAAA;QACAsD,MAAA,CAAArD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEAwD,MAAA,CAAAzD,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA2C,MAAA,CAAAzD,QAAA,CAAAuC,IAAA;IACA;EACA,qCAGAmB,qBAAAb,UAAA,EAAAc,QAAA;IACA,IAAAA,QAAA;MACA,UAAArG,iBAAA,CAAAsG,QAAA,CAAAf,UAAA;QACA,KAAAvF,iBAAA,CAAAuG,IAAA,CAAAhB,UAAA;MACA;IACA;MACA,IAAAiB,KAAA,QAAAxG,iBAAA,CAAAyG,OAAA,CAAAlB,UAAA;MACA,IAAAiB,KAAA;QACA,KAAAxG,iBAAA,CAAA0G,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAApB,aAAA,QAAApF,iBAAA,CAAA+E,MAAA,UAAAvF,YAAA,CAAAuF,MAAA;EACA,mCAEA4B,mBAAApB,UAAA;IAAA,IAAAqB,MAAA;IACA,IAAAJ,KAAA,QAAA3B,iBAAA,CAAA4B,OAAA,CAAAlB,UAAA;IACA,IAAAiB,KAAA;MACA;MACA,KAAA3B,iBAAA,CAAA6B,MAAA,CAAAF,KAAA;MACA;MACA,SAAAzG,SAAA;QACA,KAAAA,SAAA;QACA;QACA,KAAAP,YAAA,CAAAuE,OAAA,WAAA8C,QAAA;UACA,IAAAA,QAAA,CAAAtB,UAAA,KAAAA,UAAA,KAAAqB,MAAA,CAAA/B,iBAAA,CAAAyB,QAAA,CAAAO,QAAA,CAAAtB,UAAA;YACAqB,MAAA,CAAA/B,iBAAA,CAAA0B,IAAA,CAAAM,QAAA,CAAAtB,UAAA;UACA;QACA;MACA;IACA;MACA;MACA,KAAAV,iBAAA,CAAA0B,IAAA,CAAAhB,UAAA;IACA;EACA,mCAEAuB,mBAAAD,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA;IACA,KAAApC,mBAAA,GAAAoC,QAAA,CAAAjH,YAAA;IACA,KAAA+E,mBAAA;EACA,mCAEAoC,mBAAAF,QAAA;IACA;IACA,IAAAG,cAAA,OAAAtD,cAAA,CAAAxD,OAAA,MAAAwD,cAAA,CAAAxD,OAAA,MACA2G,QAAA;MACAtB,UAAA;MAAA;MACA0B,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAA1C,mBAAA,GAAAsC,cAAA;IACA,KAAAvC,mBAAA,QAAA4C,2BAAA,CAAAR,QAAA,CAAAjH,YAAA;IACA,KAAA+E,mBAAA;EACA,4CAGA0C,4BAAA7C,IAAA;IACA,IAAAb,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAa,IAAA,KAAAA,IAAA;EACA,qCAEA8C,qBAAAT,QAAA;IAAA,IAAAU,MAAA;IACA,IAAAzH,eAAA,GAAA+G,QAAA,CAAA/G,eAAA,CAAA0H,OAAA;IACA,IAAAC,cAAA,GAAA3H,eAAA,CAAAiF,MAAA,QAAAjF,eAAA,CAAA4H,SAAA,kBAAA5H,eAAA;IACA,KAAA6F,QAAA,0CAAAT,MAAA,CAAAuC,cAAA;MACA7B,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACA,IAAA0C,qBAAA,EAAAc,QAAA,CAAAtB,UAAA,EAAAlC,IAAA;QACAkE,MAAA,CAAA7E,QAAA,CAAA8C,OAAA;QACA+B,MAAA,CAAA1E,eAAA;QACA0E,MAAA,CAAAzE,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEA4E,MAAA,CAAA7E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEAgF,0BAAA;IACA,KAAAhD,mBAAA;IACA,KAAA9B,eAAA;IACA,KAAAC,aAAA;EACA,yCAEA8E,yBAAA;IACA,KAAAvD,kBAAA;IACA,KAAA/C,mBAAA;IACA,KAAAuB,eAAA;IACA,KAAAC,aAAA;EACA,kCAKA+E,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAA7H,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGA+C,mBAAA;IACA;IACA,KAAAX,eAAA;IACA,KAAAkH,mBAAA;;IAEA;IACA,KAAA5G,eAAA;IACA,KAAAC,WAAA;;IAEA;IACA,KAAA4G,WAAA;IACA,KAAAhH,oBAAA;;IAEA;IACA,KAAAiH,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAAC,aAAA;MACAhI,OAAA;MACAC,cAAA;IACA;EACA,yCAGAgI,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAJ,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAAzG,SAAA;MACA,IAAA6G,eAAA,GAAAD,MAAA,CAAAE,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;EAEA,gCAGAC,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,muDAuBA/H,IAAA;;IAEA;IACA,SAAAU,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAsH,OAAA,CAAAD,YAAA;IAEA;MACA;MACA,KAAAvH,SAAA;QACA,IAAAsH,MAAA,CAAApH,UAAA,IAAAoH,MAAA,CAAAlH,iBAAA;UACAkH,MAAA,CAAApH,UAAA,CAAAsH,OAAA,CAAAD,YAAA;QAEA;MACA;IACA;;IAEA;IACA,KAAAH,kBAAA;;IAEA;IACA,KAAAnG,QAAA,CAAA8C,OAAA;EAGA,sCAGA0D,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA9E,IAAA,kFACA8E,IAAA,CAAA9E,IAAA,4EACA8E,IAAA,CAAA5K,IAAA,CAAA8K,QAAA,aAAAF,IAAA,CAAA5K,IAAA,CAAA8K,QAAA;IACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;IAEA,KAAAH,WAAA;MACA,KAAA7G,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAA8G,OAAA;MACA,KAAA/G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAT,UAAA,CAAAjD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAAgJ,WAAA;IACA,KAAAC,SAAA;IAIA;EACA,oCAGAyB,oBAAArG,QAAA,EAAAgG,IAAA;IAAA,IAAAM,MAAA;IAGA,IAAAtG,QAAA,CAAAuG,IAAA;MACA;MACA,KAAA5B,WAAA;MACA,KAAAC,SAAA;;MAIA;MACA,KAAA/G,eAAA;MACA,KAAAC,WAAA;;MAEA;MACA0I,UAAA;QACAF,MAAA,CAAAlB,2BAAA;QACAkB,MAAA,CAAA1B,SAAA;MACA;;MAEA;MACA,KAAAlH,oBAAA;;MAEA;MACA,IAAAsC,QAAA,CAAAyG,SAAA,IAAAzG,QAAA,CAAAyG,SAAA,CAAAhF,MAAA;QACA,KAAA5D,eAAA,GAAAmC,QAAA,CAAAyG,SAAA,CAAA1E,GAAA,WAAAwB,QAAA;UAAA,WAAAnD,cAAA,CAAAxD,OAAA,MAAAwD,cAAA,CAAAxD,OAAA,MACA2G,QAAA;YACAmD,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAhC,WAAA;QACA,KAAA5G,WAAA,GAAAkC,QAAA,CAAA2G,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAA5G,QAAA,CAAA2G,MAAA,GAAA3G,QAAA,CAAA2G,MAAA,CAAAlF,MAAA;QACA,IAAAmF,UAAA;UACA,KAAAxH,QAAA,CAAA8C,OAAA,mCAAAN,MAAA,CAAA5B,QAAA,CAAAyG,SAAA,CAAAhF,MAAA,sCAAAG,MAAA,CAAAgF,UAAA;QACA;UACA,KAAAxH,QAAA,CAAA8C,OAAA,mCAAAN,MAAA,CAAA5B,QAAA,CAAAyG,SAAA,CAAAhF,MAAA;QACA;MAGA;QACA,KAAArC,QAAA,CAAAC,KAAA;QACA,KAAAxB,eAAA;QACA,KAAAC,WAAA,GAAAkC,QAAA,CAAA2G,MAAA;MAGA;;MAEA;MACA,IAAA3G,QAAA,CAAA6G,eAAA;QACA,KAAAC,gBAAA,CAAA9G,QAAA,CAAA6G,eAAA;QACA,KAAAtJ,eAAA,GAAAyC,QAAA,CAAA6G,eAAA;QACA,KAAApC,mBAAA,GAAAzE,QAAA,CAAA6G,eAAA;MAEA;;MAEA;MACAL,UAAA;QACAF,MAAA,CAAA5I,oBAAA;MACA;IACA;MAEA,KAAA0B,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAA+G,GAAA;MACA;MACA,KAAApC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAoC,kBAAA3H,KAAA,EAAA2G,IAAA;IAEA,KAAA5G,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAsF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAqC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAArJ,eAAA,CAAA4C,OAAA,WAAA8C,QAAA;MACA2D,MAAA,CAAAC,IAAA,CAAA5D,QAAA;IACA;EACA,QAAA5G,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,6BAGAiM,eAAAlE,KAAA;IACA,IAAAK,QAAA,QAAA1F,eAAA,CAAAqF,KAAA;IACA,KAAAiE,IAAA,CAAA5D,QAAA,gBAAAA,QAAA,CAAAmD,SAAA;EACA,mCAGAW,mBAAA;IAAA,IAAAC,OAAA;IACA,KAAA5C,WAAA,SAAAA,WAAA;IACA,KAAA7G,eAAA,CAAA4C,OAAA,WAAA8C,QAAA;MACA+D,OAAA,CAAAH,IAAA,CAAA5D,QAAA,gBAAA+D,OAAA,CAAA5C,WAAA;IACA;EAEA,8BAGA6C,cAAA;IAAA,IAAAC,OAAA;IACA,SAAA3J,eAAA,CAAA4D,MAAA;MACA,KAAArC,QAAA,CAAAsC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,6BAAAT,MAAA,MAAA/D,eAAA,CAAA4D,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAnB,IAAA;MACAyH,OAAA,CAAAC,eAAA;IACA,GAAAvH,KAAA;EACA,gCAGAuH,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAA/K,OAAA,mBAAAgL,aAAA,CAAAhL,OAAA,IAAAiL,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAAhI,QAAA,EAAAiI,EAAA;MAAA,WAAAL,aAAA,CAAAhL,OAAA,IAAAsL,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAA1L,OAAA,EAAA8K,OAAA,CAAA7J,eAAA;YAEA,IAAA6J,OAAA,CAAA7C,aAAA,CAAAhI,OAAA;cACAkL,iBAAA,CAAAlL,OAAA;YACA;;YAEA;YACAmL,UAAA;cACArM,MAAA,EAAA+L,OAAA,CAAA/L,MAAA;cACA8K,SAAA,EAAAsB,iBAAA;cACAjL,cAAA,EAAA4K,OAAA,CAAA7C,aAAA,CAAA/H;YACA;YAAAqL,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAG,kCAAA,EAAAP,UAAA;UAAA;YAAAhI,QAAA,GAAAmI,QAAA,CAAAK,CAAA;YAAA,MAEAxI,QAAA,CAAAuG,IAAA;cAAA4B,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAAtI,QAAA,CAAA8C,OAAA,6BAAAN,MAAA,CAAAmG,iBAAA,CAAAtG,MAAA;YAAA0G,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAK,KAAA,CAAAzI,QAAA,CAAA+G,GAAA;UAAA;YAEAW,OAAA,CAAA1J,mBAAA;YACA0J,OAAA,CAAAnK,eAAA;YACAmK,OAAA,CAAAjD,mBAAA;YACAiD,OAAA,CAAA7J,eAAA;YACA6J,OAAA,CAAA5J,WAAA;YAIA4J,OAAA,CAAAnI,eAAA;YACAmI,OAAA,CAAAlI,aAAA;YAAA2I,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAK,CAAA;YAGAd,OAAA,CAAAtI,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAA8I,QAAA,CAAAO,CAAA;QAAA;MAAA,GAAAZ,OAAA;IAAA;EAEA,+BAGA1J,eAAA;IAAA,IAAAuK,OAAA;IACA,SAAApK,iBAAA;MACA;IACA;;IAEA;IACA,KAAAqK,MAAA,CAAAC,QAAA;MAEA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAAzK,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAA0K,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QAEA;MACA;;MAEA;MACAA,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAA/K,SAAA;QACA;QACA,KAAAyK,MAAA,CAAAC,QAAA,KAAAD,MAAA,CAAAC,QAAA,CAAA3E,OAAA;UAEAyE,OAAA,CAAAQ,kBAAA;UACA;QACA;QAEA;UACA;UACAR,OAAA,CAAAtK,UAAA,GAAAuK,MAAA,CAAAC,QAAA,CAAA3E,OAAA,6BAAAvH,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAwM,MAAA;YAAA;YACAC,OAAA,GACA;cAAAjO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,GACA;cAAAlO,IAAA;cAAAkO,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAAlN,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACAiN,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA,GAEA;YACAC,aAAA,WAAAA,cAAA;cAGA,IAAAC,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA9O,IAAA;gBACA,IAAAgP,MAAA,CAAAC,OAAA;kBAGA;kBACAlE,UAAA;oBACA,IAAAmE,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAC,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;wBACA;sBACA,SAAAC,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACA3E,UAAA;sBAAA,OAAAyE,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAAtL,KAAA;UAGA;UACA;YACAsJ,OAAA,CAAAtK,UAAA,GAAAuK,MAAA,CAAAC,QAAA,CAAA3E,OAAA;cACAkF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAAlN,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAiN,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBAGA,IAAAD,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA9O,IAAA;oBACA,IAAAgP,MAAA,CAAAC,OAAA;sBAGA;sBACAlE,UAAA;wBACA,IAAAmE,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAC,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;4BACA;0BACA,SAAAC,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACA3E,UAAA;0BAAA,OAAAyE,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;UAEA,SAAAU,aAAA;YAEA1C,OAAA,CAAAQ,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAR,OAAA,CAAAtK,UAAA,IAAAsK,OAAA,CAAAtK,UAAA,CAAAmM,EAAA;UACA7B,OAAA,CAAAtK,UAAA,CAAAmM,EAAA;YACA,IAAAc,UAAA,GAAA3C,OAAA,CAAAtK,UAAA,CAAAkN,OAAA;YACA,IAAAC,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;;YAEA;YACA3C,OAAA,CAAAlE,mBAAA,GAAAkE,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAApL,eAAA,GAAAoL,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;UAGA;QACA;;QAEA;QACA7C,OAAA,CAAAtK,UAAA,CAAAmM,EAAA;UACAhE,UAAA;YACA,IAAA8E,UAAA,GAAA3C,OAAA,CAAAtK,UAAA,CAAAkN,OAAA;YACA,IAAAC,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;;YAEA;YACA3C,OAAA,CAAAlE,mBAAA,GAAAkE,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAApL,eAAA,GAAAoL,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;UAGA;QACA;;QAEA;QACA7C,OAAA,CAAAtK,UAAA,CAAAmM,EAAA;UACA7B,OAAA,CAAApK,iBAAA;UACA;UACAoK,OAAA,CAAAtK,UAAA,CAAAsH,OAAA;QACA;MACA;IAEA,SAAAtG,KAAA;MAEA;MACA,KAAAyJ,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAA8C,OAAA;IACA,IAAA7C,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAA8C,QAAA,GAAA7C,QAAA,CAAA8C,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA;MACAJ,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAAO,gBAAA,oBAAAjB,CAAA;QACAS,OAAA,CAAArO,eAAA,GAAA4N,CAAA,CAAAkB,MAAA,CAAAJ,KAAA;QACAL,OAAA,CAAAnH,mBAAA,GAAA0G,CAAA,CAAAkB,MAAA,CAAAJ,KAAA;MAEA;MAEAlD,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAAuD,WAAA,CAAAT,QAAA;MACA,KAAAtN,iBAAA;IACA;EACA,8BAGAgO,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAAzD,QAAA,CAAA8C,aAAA;IACAW,GAAA,CAAAvD,SAAA,GAAAsD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGA7F,iBAAA8F,OAAA;IAEA,SAAAvO,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAsH,OAAA,CAAAiH,OAAA;IACA;MACA;MACA,KAAArP,eAAA,GAAAqP,OAAA;MACA,KAAAnI,mBAAA,GAAAmI,OAAA;IACA;EACA,yBAKAlO,SAAAmO,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAzL,MAAA,EAAA0L,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACAC,YAAA,CAAAR,OAAA;QACAF,IAAA,CAAAW,KAAA,SAAAL,IAAA;MACA;MACAI,YAAA,CAAAR,OAAA;MACAA,OAAA,GAAAvG,UAAA,CAAA8G,KAAA,EAAAR,IAAA;IACA;EACA,sCAGArB,sBAAAmB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAa,aAAA,GAAA7E,MAAA,CAAA8E,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAvJ,OAAA;IAEA,OAAA0I,OAAA,CAAA1I,OAAA,CAAA0J,QAAA;EACA,QAAAjR,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,4BAGAwD,cAAA;IACA,UAAApB,eAAA,CAAAI,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAAgQ,WAAA,QAAAC,oBAAA,MAAAxQ,eAAA;MACA;MACA,KAAAM,eAAA,GAAAiQ,WAAA,CAAArH,SAAA,CAAA1E,GAAA,WAAAwB,QAAA;QAAA,WAAAnD,cAAA,CAAAxD,OAAA,MAAAwD,cAAA,CAAAxD,OAAA,MACA2G,QAAA;UACAmD,SAAA;QAAA;MAAA,CACA;MACA,KAAA5I,WAAA,GAAAgQ,WAAA,CAAAnH,MAAA;IAGA,SAAAtH,KAAA;MAEA,KAAAvB,WAAA,cAAAuB,KAAA,CAAA2O,OAAA;MACA,KAAAnQ,eAAA;IACA;EACA,qCAGAkQ,qBAAAnB,OAAA;IACA,IAAAnG,SAAA;IACA,IAAAE,MAAA;IAEA,KAAAiG,OAAA,WAAAA,OAAA;MAEA;QAAAnG,SAAA,EAAAA,SAAA;QAAAE,MAAA;MAAA;IACA;IAEA;MAGA;MACA,IAAA+F,WAAA,QAAAf,uBAAA,CAAAiB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAA/O,IAAA,GAAA8D,MAAA;QAEA;UAAAgF,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;;MAEA;MACA,IAAAsH,KAAA,GAAAvB,WAAA,CAAAwB,KAAA,OAAAnM,GAAA,WAAAoM,IAAA;QAAA,OAAAA,IAAA,CAAAxQ,IAAA;MAAA,GAAAyQ,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAA1M,MAAA;MAAA;MAGA,IAAAwM,KAAA,CAAAxM,MAAA;QAEA;UAAAgF,SAAA,EAAAA,SAAA;UAAAE,MAAA;QAAA;MACA;MAIA,IAAA0H,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAN,IAAA,UAAAO,mBAAA,CAAAP,IAAA;QAEA,IAAAK,eAAA;UACA;UACA,IAAAH,oBAAA,CAAA5M,MAAA;YACA;cACA,IAAAkN,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACA,IAAAC,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBACApI,SAAA,CAAAxD,IAAA,CAAA4L,cAAA;cACA;YACA,SAAAxP,KAAA;cACAsH,MAAA,CAAA1D,IAAA,WAAArB,MAAA,CAAA0M,cAAA,uCAAA1M,MAAA,CAAAvC,KAAA,CAAA2O,OAAA;YAEA;UACA;;UAEA;UACAK,oBAAA,IAAAF,IAAA;UACAG,cAAA;QACA;UACA;UACA,IAAAD,oBAAA,CAAA5M,MAAA;YACA4M,oBAAA,CAAApL,IAAA,CAAAkL,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAE,oBAAA,CAAA5M,MAAA;QACA;UACA,IAAAkN,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YACApI,SAAA,CAAAxD,IAAA,CAAA4L,eAAA;UACA;QACA,SAAAxP,KAAA;UACAsH,MAAA,CAAA1D,IAAA,WAAArB,MAAA,CAAA0M,cAAA,uCAAA1M,MAAA,CAAAvC,KAAA,CAAA2O,OAAA;QAEA;MACA;IAEA,SAAA3O,KAAA;MACAsH,MAAA,CAAA1D,IAAA,0CAAArB,MAAA,CAAAvC,KAAA,CAAA2O,OAAA;MACAe,OAAA,CAAA1P,KAAA,cAAAA,KAAA;IACA;IAEA0P,OAAA,CAAAC,GAAA,WAAAvI,SAAA,CAAAhF,MAAA,UAAAkF,MAAA,CAAAlF,MAAA;IACA;MAAAgF,SAAA,EAAAA,SAAA;MAAAE,MAAA,EAAAA;IAAA;EACA,oCAGA8H,oBAAAN,IAAA;IACA;IACA;IACA;IACA,wBAAAc,IAAA,CAAAd,IAAA;EACA,oCAGAO,oBAAAP,IAAA;IACA;IACA;IACA,mBAAAc,IAAA,CAAAd,IAAA;EACA,uCAGAW,uBAAAH,YAAA;IACA,IAAAV,KAAA,GAAAU,YAAA,CAAAT,KAAA,OAAAnM,GAAA,WAAAoM,IAAA;MAAA,OAAAA,IAAA,CAAAxQ,IAAA;IAAA,GAAAyQ,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA1M,MAAA;IAAA;IAEA,IAAAwM,KAAA,CAAAxM,MAAA;MACA,UAAAgH,KAAA;IACA;IAEA,IAAAnM,YAAA;IACA,IAAAE,eAAA;IACA,IAAA0S,iBAAA;;IAEA;IACA,SAAAX,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;MACA,IAAAY,SAAA,GAAAhB,IAAA,CAAAiB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;;QAEA;QACA,IAAAE,QAAA,CAAArM,QAAA;UACA1G,YAAA;QACA,WAAA+S,QAAA,CAAArM,QAAA;UACA1G,YAAA;QACA,WAAA+S,QAAA,CAAArM,QAAA;UACA1G,YAAA;QACA,WAAA+S,QAAA,CAAArM,QAAA;UACA1G,YAAA;QACA,WAAA+S,QAAA,CAAArM,QAAA;UACA1G,YAAA;QACA;;QAEA;QACA,IAAAgT,gBAAA,GAAAnB,IAAA,CAAAjK,OAAA,iBAAAvG,IAAA;QACA,IAAA2R,gBAAA;UACA9S,eAAA,GAAA8S,gBAAA;UACAJ,iBAAA,GAAAX,CAAA;QACA;UACAW,iBAAA,GAAAX,CAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAW,iBAAA;MACAA,iBAAA;IACA;;IAEA;IACA,SAAAX,EAAA,GAAAW,iBAAA,EAAAX,EAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,EAAA;MACA,IAAAJ,KAAA,GAAAF,KAAA,CAAAM,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAN,KAAA;QACA;QACA3R,eAAA,GAAA2R,KAAA,CAAAjK,OAAA,uBAAAvG,IAAA;QACAuR,iBAAA,GAAAX,EAAA;QACA;MACA,YAAA/R,eAAA;QACA;QACAA,eAAA,GAAA2R,KAAA;QACAe,iBAAA,GAAAX,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAW,iBAAA,EAAAX,GAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,GAAA;MACA,IAAAJ,MAAA,GAAAF,KAAA,CAAAM,GAAA;;MAEA;MACA,SAAAgB,YAAA,CAAApB,MAAA,UAAAqB,YAAA,CAAArB,MAAA,KACA,KAAAsB,iBAAA,CAAAtB,MAAA,UAAAuB,gBAAA,CAAAvB,MAAA;QACA;MACA;;MAEA;MACA,IAAAwB,SAAA,GAAAxB,MAAA;MACA;MACA,SAAAM,mBAAA,CAAAN,MAAA;QACAwB,SAAA,GAAAxB,MAAA,CAAAjK,OAAA,uBAAAvG,IAAA;MACA;MAEA,IAAAgS,SAAA;QACA,IAAAnT,eAAA;UACAA,eAAA,WAAAmT,SAAA;QACA;UACAnT,eAAA,GAAAmT,SAAA;QACA;MACA;IACA;IAEA,KAAAnT,eAAA;MACA,UAAAiM,KAAA;IACA;;IAEA;IACA,IAAAmH,oBAAA,GAAApT,eAAA,CAAAmB,IAAA;IACA;IACA,wBAAAsR,IAAA,CAAAW,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAA1L,OAAA,0BAAAvG,IAAA;IACA;;IAEA;IACA,IAAAiS,oBAAA,CAAA5M,QAAA;MACA4M,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAArM,QAAA;MACAjH,YAAA,EAAAA,YAAA;MACA4E,IAAA,EAAA5E,YAAA;MACAwT,QAAA,OAAAC,kBAAA,CAAAzT,YAAA;MACAE,eAAA,EAAAoT,oBAAA;MACAhD,OAAA,EAAAgD,oBAAA;MACArT,UAAA;MAAA;MACAyT,WAAA;MACAC,OAAA;MACAC,aAAA;MACAxJ,SAAA;IACA;;IAEA;IACA,IAAAyJ,YAAA,QAAAC,qBAAA,CAAAnC,KAAA;IACA1K,QAAA,CAAA0M,OAAA,GAAAE,YAAA,CAAAF,OAAA;;IAEA;IACA,IAAA3T,YAAA,mBAAAiH,QAAA,CAAA0M,OAAA,CAAAxO,MAAA;MACA;MACAnF,YAAA;MACAiH,QAAA,CAAAjH,YAAA,GAAAA,YAAA;MACAiH,QAAA,CAAArC,IAAA,GAAA5E,YAAA;MACAiH,QAAA,CAAAuM,QAAA,QAAAC,kBAAA,CAAAzT,YAAA;IACA;;IAEA;IACA,KAAA+T,0BAAA,CAAApC,KAAA,EAAA1K,QAAA;;IAEA;IACA,IAAAjH,YAAA,iBAAAiH,QAAA,CAAA2M,aAAA,IAAA3M,QAAA,CAAA2M,aAAA,CAAAzO,MAAA;MACA;MACA,kBAAAwN,IAAA,CAAA1L,QAAA,CAAA2M,aAAA;QACA5T,YAAA;QACAiH,QAAA,CAAAjH,YAAA,GAAAA,YAAA;QACAiH,QAAA,CAAArC,IAAA,GAAA5E,YAAA;QACAiH,QAAA,CAAAuM,QAAA,QAAAC,kBAAA,CAAAzT,YAAA;MACA;IACA;;IAEA;IACAiH,QAAA,CAAA/G,eAAA,QAAAqT,oBAAA,CAAAtM,QAAA,CAAA/G,eAAA;IACA+G,QAAA,CAAAqJ,OAAA,GAAArJ,QAAA,CAAA/G,eAAA;IAEA,OAAA+G,QAAA;EACA,6BAGAgM,aAAApB,IAAA;IACA;IACA,6BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAqB,aAAArB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,kCAGAsB,kBAAAtB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,iCAGAuB,iBAAAvB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,mCAGA4B,mBAAA7O,IAAA;IACA,IAAAb,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAa,IAAA;EACA,QAAAvE,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAGAmV,kBAAA1D,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAA2D,gBAAA,GAAA3D,OAAA,CAAA1I,OAAA,mDAAAkL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAArB,KAAA;;QAGA;QACA,IAAAqB,GAAA,CAAAzF,UAAA,eAAAyF,GAAA,CAAAzF,UAAA,gBAAAyF,GAAA,CAAAzF,UAAA;UACA,OAAAoE,KAAA;QACA;;QAEA;QACA,IAAAuB,OAAA,8BAAAF,GAAA,CAAAzF,UAAA,QAAAyF,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAAhP,MAAA,CAAA4O,MAAA,YAAA5O,MAAA,CAAA+O,OAAA,QAAA/O,MAAA,CAAA8O,KAAA;QACA,OAAAE,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAAlR,KAAA;MACA0P,OAAA,CAAA1P,KAAA,iBAAAA,KAAA;MACA,OAAAuN,OAAA;IACA;EACA,2CAGAlB,2BAAAkB,OAAA;IAAA,IAAAiE,OAAA;IACA,KAAAjE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAA2D,gBAAA,GAAA3D;MACA;MAAA,CACA1I,OAAA,oDAAAkL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAAzF,UAAA,aAAAyF,GAAA,CAAAzF,UAAA;UACA,IAAA2F,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAA7O,MAAA,CAAA4O,MAAA,YAAA5O,MAAA,CAAA+O,OAAA,QAAA/O,MAAA,CAAA8O,KAAA;QACA;QACA,OAAAtB,KAAA;MACA;MACA;MAAA,CACAlL,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAAqM,gBAAA,CAAA5S,IAAA;IACA,SAAA0B,KAAA;MACA0P,OAAA,CAAA1P,KAAA,qCAAAA,KAAA;MACA,OAAAuN,OAAA;IACA;EACA,wCAGAjB,wBAAAiB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAAkE,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAApE,OAAA,CAAA1I,OAAA,2BAAAkL,KAAA;QACA0B,MAAA,CAAA7N,IAAA,CAAAmM,KAAA;QACA,gCAAAxN,MAAA,CAAAmP,UAAA;MACA;;MAEA;MACA,IAAArE,WAAA,GAAAsE,uBAAA,CACA9M,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAA+M,YAAA,GAAAvE,WAAA;MACAoE,MAAA,CAAArQ,OAAA,WAAAyQ,GAAA,EAAAhO,KAAA;QACA,IAAA8I,WAAA,0BAAApK,MAAA,CAAAsB,KAAA;QACA,IAAA+N,YAAA,CAAAjO,QAAA,CAAAgJ,WAAA;UACAiF,YAAA,GAAAA,YAAA,CAAA/M,OAAA,CAAA8H,WAAA,EAAAkF,GAAA;QACA;MACA;MAEA,OAAAD,YAAA,CAAAtT,IAAA;IACA,SAAA0B,KAAA;MACA0P,OAAA,CAAA1P,KAAA,kCAAAA,KAAA;MACA,OAAAuN,OAAA;IACA;EACA,sCAGAwD,sBAAAnC,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IAEA,KAAA7C,KAAA,CAAAgE,OAAA,CAAAnD,KAAA,KAAAkD,UAAA,QAAAA,UAAA,IAAAlD,KAAA,CAAAxM,MAAA;MACAsN,OAAA,CAAAsC,IAAA;MACA;QAAApB,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAA1B,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;QAEA,KAAAJ,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAmD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;QACA,IAAAkC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAA3T,IAAA;UAEA,IAAA4T,SAAA,IAAAE,aAAA;YACAxB,OAAA,CAAAhN,IAAA;cACAsO,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACA7E,OAAA,EAAA6E;YACA;UACA;QACA,gBAAAjC,YAAA,CAAArB,IAAA,UAAAsB,iBAAA,CAAAtB,IAAA,UAAAuB,gBAAA,CAAAvB,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAAwD,oBAAA,GAAAxD,IAAA,CAAAiB,KAAA;UACA,IAAAuC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAAzD,IAAA,CAAAD,KAAA;YAAA,IAAA2D,SAAA,OAAAC,2BAAA,CAAAlV,OAAA,EACAgV,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAzJ,CAAA,IAAA5D,IAAA;gBAAA,IAAAyN,YAAA,GAAAF,KAAA,CAAA9F,KAAA;gBACA,KAAAgG,YAAA;gBAEA,IAAA7C,KAAA,GAAA6C,YAAA,CAAA7C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAmC,UAAA,GAAAnC,KAAA,IAAAoC,WAAA;kBACA,IAAAC,cAAA,GAAArC,KAAA,MAAAA,KAAA,IAAAzR,IAAA;kBAEA,IAAA4T,UAAA,IAAAE,cAAA;oBACAxB,OAAA,CAAAhN,IAAA;sBACAsO,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACA7E,OAAA,EAAA6E;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAA1G,CAAA,CAAA+G,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAA9S,KAAA;MACA0P,OAAA,CAAA1P,KAAA,eAAAA,KAAA;IACA;IAEA;MAAA4Q,OAAA,EAAAA;IAAA;EACA,2CAGAI,2BAAApC,KAAA,EAAA1K,QAAA;IACA,SAAAgL,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACA7O,QAAA,CAAA2M,aAAA,QAAAmC,gBAAA,CAAAD,WAAA,KAAA7O,QAAA,CAAAjH,YAAA;QACA;MACA;;MAEA;MACA,IAAAgW,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACA/O,QAAA,CAAAyM,WAAA,GAAAsC,gBAAA,IAAA3U,IAAA;QACA;MACA;;MAEA;MACA,IAAA4U,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAhW,UAAA,GAAAgW,eAAA;QACA;QACA,IAAAhW,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAyG,QAAA,CAAAzG,UAAA;UACAgH,QAAA,CAAAhH,UAAA,GAAAA,UAAA;QACA;UACAwS,OAAA,CAAAsC,IAAA,iBAAA9U,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAAgH,QAAA,CAAA2M,aAAA;MACA3M,QAAA,CAAA2M,aAAA,QAAAsC,gCAAA,CAAAjP,QAAA,CAAA/G,eAAA,EAAA+G,QAAA,CAAAjH,YAAA;IACA;EACA,iDAGAkW,iCAAAhW,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAAiW,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAAlR,MAAA,EAAAiR,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAArW,eAAA,CAAA4S,KAAA,CAAAwD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAApR,MAAA;UACA;UACA,IAAAqR,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAApR,MAAA;UACA,IAAAsR,MAAA,GAAAD,SAAA,CAAA5O,OAAA,sBAAAvG,IAAA;UAEA,IAAAoV,MAAA;YACA,YAAAV,gBAAA,CAAAU,MAAA,EAAAzW,YAAA;UACA;QACA;MACA;IACA,SAAA+C,KAAA;MACA0P,OAAA,CAAA1P,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGAgT,iBAAAW,UAAA,EAAA1W,YAAA;IACA,KAAA0W,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAArV,IAAA;MAEA,KAAAsV,aAAA;QACA;MACA;MAEA,IAAA3W,YAAA;QACA;QACA,OAAA2W,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAzB,WAAA;MACA;IACA,SAAAnS,KAAA;MACA0P,OAAA,CAAA1P,KAAA,gBAAAA,KAAA;MACA,OAAA2T,UAAA;IACA;EACA,oCAGAE,oBAAAtG,OAAA;IACA,IAAAuG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAAjE,KAAA;IACA,IAAAkE,WAAA;IAEA,QAAAlE,KAAA,GAAAgE,SAAA,CAAAG,IAAA,CAAA3G,OAAA;MACA,IAAA0G,WAAA;QACA;QACAH,QAAA,CAAAlQ,IAAA;UACA/B,IAAA,EAAAoS,WAAA;UACA1G,OAAA,EAAAA,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAjE,KAAA,CAAAlM,KAAA,EAAAvF,IAAA;QACA;MACA;MACA2V,WAAA,GAAAlE,KAAA;MACAiE,SAAA,GAAAjE,KAAA,CAAAlM,KAAA,GAAAkM,KAAA,IAAA3N,MAAA;IACA;;IAEA;IACA,IAAA6R,WAAA;MACAH,QAAA,CAAAlQ,IAAA;QACA/B,IAAA,EAAAoS,WAAA;QACA1G,OAAA,EAAAA,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAA1V,IAAA;MACA;IACA;IAEA,OAAAwV,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAAjN,SAAA;IACA,IAAAnK,YAAA,QAAAqX,mBAAA,CAAAF,OAAA,CAAAvS,IAAA;;IAEA;IACA,IAAA0S,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA7G,OAAA;IAEAgH,cAAA,CAAAnT,OAAA,WAAAqT,KAAA,EAAA5Q,KAAA;MACA;QACA,IAAAK,QAAA,GAAAmQ,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAAxX,YAAA,EAAA4G,KAAA;QACA,IAAAK,QAAA;UACAkD,SAAA,CAAAxD,IAAA,CAAAM,QAAA;QACA;MACA,SAAAlE,KAAA;QACA,UAAAoJ,KAAA,UAAA7G,MAAA,CAAAsB,KAAA,0CAAAtB,MAAA,CAAAvC,KAAA,CAAA2O,OAAA;MACA;IACA;IAEA,OAAAvH,SAAA;EACA,sCAGAoN,sBAAAjH,OAAA;IACA,IAAAoH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAAjE,KAAA;IAEA,QAAAA,KAAA,GAAA6E,WAAA,CAAAV,IAAA,CAAA3G,OAAA;MACA,IAAAyG,SAAA;QACA;QACAW,MAAA,CAAA/Q,IAAA,CAAA2J,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAjE,KAAA,CAAAlM,KAAA,EAAAvF,IAAA;MACA;MACA0V,SAAA,GAAAjE,KAAA,CAAAlM,KAAA;IACA;;IAEA;IACA,IAAAmQ,SAAA,GAAAzG,OAAA,CAAAnL,MAAA;MACAuS,MAAA,CAAA/Q,IAAA,CAAA2J,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAA1V,IAAA;IACA;IAEA,OAAAqW,MAAA,CAAA5F,MAAA,WAAA0F,KAAA;MAAA,OAAAA,KAAA,CAAArS,MAAA;IAAA;EACA,QAAA9E,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGA4Y,mBAAAD,KAAA,EAAAxX,YAAA;IACA,IAAA2R,KAAA,GAAA6F,KAAA,CAAA5F,KAAA,OAAAnM,GAAA,WAAAoM,IAAA;MAAA,OAAAA,IAAA,CAAAxQ,IAAA;IAAA,GAAAyQ,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAA1M,MAAA;IAAA;IAEA,IAAAwM,KAAA,CAAAxM,MAAA;MACA,UAAAgH,KAAA;IACA;;IAEA;IACA,IAAAyL,SAAA,GAAAjG,KAAA;IACA,IAAAzR,eAAA;IACA,IAAA2X,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA9E,KAAA;IACA,IAAAgF,WAAA;MACA5X,eAAA,GAAA4X,WAAA,IAAAzW,IAAA;MACAwW,gBAAA;IACA;MACA;MACA3X,eAAA,QAAAqT,oBAAA,CAAAqE,SAAA,EAAAvW,IAAA;MACAwW,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAAlG,KAAA,CAAAxM,MAAA;MACA,IAAA0M,IAAA,GAAAF,KAAA,CAAAkG,gBAAA;MACA,SAAA5E,YAAA,CAAApB,IAAA;QACA;MACA;MACA3R,eAAA,WAAA2R,IAAA;MACAgG,gBAAA;IACA;IAEA,IAAA5Q,QAAA;MACAjH,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAmB,IAAA;MACApB,UAAA;MAAA;MACAyT,WAAA;MACAC,OAAA;MACAC,aAAA;IACA;;IAEA;IACA,IAAA5T,YAAA;MACA,IAAA6T,YAAA,QAAAkE,YAAA,CAAApG,KAAA,EAAAkG,gBAAA;MACA5Q,QAAA,CAAA0M,OAAA,GAAAE,YAAA,CAAAF,OAAA;MACAkE,gBAAA,GAAAhE,YAAA,CAAAmE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAtG,KAAA,EAAAkG,gBAAA,EAAA5Q,QAAA;;IAEA;IACAA,QAAA,CAAA/G,eAAA,QAAAqT,oBAAA,CAAAtM,QAAA,CAAA/G,eAAA;IAEA,OAAA+G,QAAA;EACA,6BAAAgM,aAGApB,IAAA;IACA,4BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAkG,aAAApG,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IACA,IAAAuE,YAAA,GAAArD,UAAA;IAEA,OAAAqD,YAAA,GAAAvG,KAAA,CAAAxM,MAAA;MACA,IAAA0M,IAAA,GAAAF,KAAA,CAAAuG,YAAA;MACA,IAAAlD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;MAEA,KAAAkC,WAAA;QACA;MACA;MAEArB,OAAA,CAAAhN,IAAA;QACAsO,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAA3T,IAAA;MACA;MAEA6W,YAAA;IACA;IAEA;MAAAvE,OAAA,EAAAA,OAAA;MAAAqE,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAtG,KAAA,EAAAkD,UAAA,EAAA5N,QAAA;IACA,SAAAgL,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAAxM,MAAA,EAAA8M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACA7O,QAAA,CAAA2M,aAAA,QAAAuE,WAAA,CAAArC,WAAA,KAAA7O,QAAA,CAAAjH,YAAA;QACA;MACA;;MAEA;MACA,IAAAgW,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACA/O,QAAA,CAAAyM,WAAA,GAAAsC,gBAAA,IAAA3U,IAAA;QACA;MACA;;MAEA;MACA,IAAA4U,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAhW,UAAA,GAAAgW,eAAA;QACA;QACA,IAAAhW,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAyG,QAAA,CAAAzG,UAAA;UACAgH,QAAA,CAAAhH,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAAgH,QAAA,CAAA2M,aAAA;MACA3M,QAAA,CAAA2M,aAAA,QAAAwE,wBAAA,CAAAnR,QAAA,CAAA/G,eAAA,EAAA+G,QAAA,CAAAjH,YAAA;IACA;EACA,yCAKAoY,yBAAA9H,OAAA,EAAAtQ,YAAA;IACA;IACA,IAAAqY,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAApT,MAAA,EAAAmT,GAAA;MAAA,IAAAhC,OAAA,GAAAiC,gBAAA,CAAAD,GAAA;MACA,IAAA/B,OAAA,OAAAvK,mBAAA,CAAA1L,OAAA,EAAAgQ,OAAA,CAAAkI,QAAA,CAAAlC,OAAA;MACA,IAAAC,OAAA,CAAApR,MAAA;QACA,IAAAsR,MAAA,GAAAF,OAAA,CAAAA,OAAA,CAAApR,MAAA;QACA,YAAAgT,WAAA,CAAA1B,MAAA,EAAAzW,YAAA;MACA;IACA;IAEA;EACA,oCAGAqX,oBAAAtE,QAAA;IACA,IAAAhP,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAgP,QAAA;EACA,oCAGA0F,oBAAA7T,IAAA;IACA,IAAAb,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAa,IAAA;EACA,qCAGA8T,qBAAA9T,IAAA;IACA,IAAA+T,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAA/T,IAAA;EACA,4CAOAgU,4BAAA3R,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAA/G,eAAA;MACA;IACA;IAEA,IAAAoQ,OAAA,GAAArJ,QAAA,CAAA/G,eAAA;;IAEA;IACA,SAAAiI,mBAAA,SAAAA,mBAAA,CAAAzB,QAAA;MACA;MACA,IAAAmS,WAAA,QAAAC,uBAAA,CAAA7R,QAAA,CAAA/G,eAAA,OAAAiI,mBAAA;MACA,IAAA0Q,WAAA;QACAvI,OAAA,GAAAuI,WAAA;MACA;IACA;;IAEA;IACAvI,OAAA,QAAAiD,oBAAA,CAAAjD,OAAA;IAEA,YAAA0D,iBAAA,CAAA1D,OAAA;EACA,qCAGAiD,qBAAAjD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAA5J,QAAA;MACA;MACA,OAAA4J,OAAA,CAAA1I,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAA0I,OAAA,CAAA1I,OAAA,0BAAAvG,IAAA;IACA;EACA,QAAAhB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,sCAGAia,wBAAAC,YAAA,EAAAF,WAAA;IACA,KAAAE,YAAA,KAAAF,WAAA;MACA,OAAAE,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAAnR,OAAA,uBAAAvG,IAAA;;MAEA;MACA,IAAA4X,UAAA,GAAAJ,WAAA,CAAA/F,KAAA;MAAA,IAAAoG,UAAA,OAAA1D,2BAAA,CAAAlV,OAAA,EAEA2Y,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxD,CAAA,MAAAyD,MAAA,GAAAD,UAAA,CAAApN,CAAA,IAAA5D,IAAA;UAAA,IAAAkR,SAAA,GAAAD,MAAA,CAAAxJ,KAAA;UACA,IAAA0J,aAAA,GAAAD,SAAA,CAAAxR,OAAA,iBAAAvG,IAAA;UACA;UACA,IAAAiY,kBAAA,GAAAD,aAAA,CAAAzR,OAAA,0BAAAvG,IAAA;UACA,IAAAiY,kBAAA,CAAA5S,QAAA,CAAAsS,SAAA,CAAAlR,SAAA;YACA;YACA,YAAAyL,oBAAA,CAAA6F,SAAA;UACA;QACA;;QAEA;MAAA,SAAAxD,GAAA;QAAAsD,UAAA,CAAArK,CAAA,CAAA+G,GAAA;MAAA;QAAAsD,UAAA,CAAArD,CAAA;MAAA;MACA,OAAAkD,YAAA;IACA,SAAAhW,KAAA;MACA0P,OAAA,CAAA1P,KAAA,kBAAAA,KAAA;MACA,OAAAgW,YAAA;IACA;EACA,6BAIAQ,aAAA;IACA,KAAA1Z,WAAA,CAAAC,OAAA;IACA,KAAAmD,eAAA;EACA,4BAEAuW,YAAA;IACA,KAAA3Z,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAAmD,eAAA;EACA;AAEA", "ignoreList": []}]}