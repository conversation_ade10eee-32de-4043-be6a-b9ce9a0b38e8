{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgQmF0Y2hJbXBvcnQgZnJvbSAnLi9jb21wb25lbnRzL0JhdGNoSW1wb3J0JwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCmltcG9ydCB7IGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25CYW5rRGV0YWlsIiwKICBjb21wb25lbnRzOiB7CiAgICBRdWVzdGlvbkNhcmQsCiAgICBRdWVzdGlvbkZvcm0sCiAgICBCYXRjaEltcG9ydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmimOW6k+S/oeaBrwogICAgICBiYW5rSWQ6IG51bGwsCiAgICAgIGJhbmtOYW1lOiAnJywKICAgICAgLy8g57uf6K6h5pWw5o2uCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgbXVsdGlwbGVDaG9pY2U6IDAsCiAgICAgICAganVkZ21lbnQ6IDAKICAgICAgfSwKICAgICAgLy8g6aKY55uu5YiX6KGoCiAgICAgIHF1ZXN0aW9uTGlzdDogW10sCiAgICAgIC8vIOWIhumhteWPguaVsAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rSWQ6IG51bGwsCiAgICAgICAgcXVlc3Rpb25UeXBlOiBudWxsLAogICAgICAgIGRpZmZpY3VsdHk6IG51bGwsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWxleW8gOeKtuaAgQogICAgICBleHBhbmRBbGw6IGZhbHNlLAogICAgICBleHBhbmRlZFF1ZXN0aW9uczogW10sCiAgICAgIC8vIOmAieaLqeeKtuaAgQogICAgICBzZWxlY3RlZFF1ZXN0aW9uczogW10sCiAgICAgIGlzQWxsU2VsZWN0ZWQ6IGZhbHNlLAogICAgICAvLyDooajljZXnm7jlhbMKICAgICAgcXVlc3Rpb25Gb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRRdWVzdGlvblR5cGU6ICdzaW5nbGUnLAogICAgICBjdXJyZW50UXVlc3Rpb25EYXRhOiBudWxsLAogICAgICAvLyDmibnph4/lr7zlhaUKICAgICAgaW1wb3J0RHJhd2VyVmlzaWJsZTogZmFsc2UsCiAgICAgIGJhdGNoSW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOaWh+aho+WvvOWFpeaKveWxiQogICAgICBkb2N1bWVudENvbnRlbnQ6ICcnLAogICAgICBkb2N1bWVudEh0bWxDb250ZW50OiAnJywKICAgICAgcGFyc2VkUXVlc3Rpb25zOiBbXSwKICAgICAgcGFyc2VFcnJvcnM6IFtdLAogICAgICBhbGxFeHBhbmRlZDogdHJ1ZSwKICAgICAgaXNTZXR0aW5nRnJvbUJhY2tlbmQ6IGZhbHNlLAogICAgICBkb2N1bWVudEltcG9ydERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBydWxlc0RpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBhY3RpdmVSdWxlVGFiOiAnZXhhbXBsZXMnLAogICAgICAvLyDkuIrkvKDlkozop6PmnpDnirbmgIEKICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLAogICAgICBpc1BhcnNpbmc6IGZhbHNlLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgcmV2ZXJzZTogZmFsc2UsCiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIC8vIOaWh+S7tuS4iuS8oAogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2Jpei9xdWVzdGlvbkJhbmsvdXBsb2FkRG9jdW1lbnQnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgdGhpcy4kc3RvcmUuZ2V0dGVycy50b2tlbgogICAgICB9LAogICAgICB1cGxvYWREYXRhOiB7fSwKICAgICAgLy8g5a+M5paH5pys57yW6L6R5ZmoCiAgICAgIHJpY2hFZGl0b3I6IG51bGwsCiAgICAgIGVkaXRvckluaXRpYWxpemVkOiBmYWxzZQogICAgfQogIH0sCgogIHdhdGNoOiB7CiAgICAvLyDnm5HlkKzmlofmoaPlhoXlrrnlj5jljJbvvIzoh6rliqjop6PmnpAKICAgIGRvY3VtZW50Q29udGVudDogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIC8vIOWmguaenOaYr+S7juWQjuerr+iuvue9ruWGheWuue+8jOS4jeinpuWPkeW<PERSON>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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqgBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.handleEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              setTimeout(() => {\n                this.handleEditorContentChange()\n              }, 100)\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化\n    handleEditorContentChange() {\n      const rawContent = this.richEditor.getData()\n      const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n      this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n      this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"]}]}